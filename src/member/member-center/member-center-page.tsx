/* eslint-disable react-hooks/exhaustive-deps */
import { View, Text, Image, Input } from '@tarojs/components';
import { useState, useEffect, useRef } from 'react';
import {
  AtIcon,
  AtButton,
  AtTag,
  AtToast,
  AtFloatLayout,
  AtRadio,
} from 'taro-ui';
import Taro, { useDidShow } from '@tarojs/taro';
import styles from './member-center-page.module.scss';
import { uniqBy } from 'lodash';
import img1 from '@/assets/image/member/位图@2x.png';
import nv from '@/assets/image/member/矩形@2x.png';
import birthday from '@/assets/image/member/矩形@2x(1).png';
import refreshImg from '@/assets/image/member/<EMAIL>';
import PageWithNav, {
  PageContainerRef,
} from '@/view/component/page-with-nav/page-with-nav.component';
import lishi from '@/assets/image/member/<EMAIL>';
import xiaofei from '@/assets/image/member/<EMAIL>';
import fuka from '@/assets/image/member/<EMAIL>';
import fuzhi from '@/assets/image/member/<EMAIL>';
import filterimg from '@/assets/image/member/形状@2x(1).png';
import memberImg from '@/assets/image/member/huiyuanqiaguanli@2x(1).png';
import nan from '@/assets/image/member/nan.png';
import AlphabetIndex from '@/view/page/component/alphabet-index/alphabet-index.component';

import memberStore from '../member-detail/store/member';
import { getMemberDetaillist } from '@/service/business/huiyuanguanli';

// 导入类型
declare namespace BUSINESS {
  interface TMemberDetailVo {
    memberId?: string;
    userId: string;
    businessId: string;
    memberType: string;
    balance?: number;
    registerTime?: string;
    status?: number;
    remarks?: string;
    nickName: string;
    phonenumber: string;
    sex: number;
    avatar?: string;
    birthday?: string;
    endDate?: string;
    banStatus?: string;
    transferTime?: string;
    subCards?: Array<{
      cardName: string;
      holderName: string;
      relation: string;
      balance: number;
    }>;
  }

  interface RListTMemberDetailVo {
    code?: number;
    msg?: string;
    data?: TMemberDetailVo[];
    total?: number;
  }
}

// 添加工具函数
const formatDate = (date: string | null) => {
  if (!date) return '';
  return date.split(' ')[0]; // 只取日期部分
};

const formatBalance = (balance: number | null) => {
  if (balance === null || balance === undefined) return '0.00';
  return balance.toFixed(2);
};

// 获取昵称首字
const getFirstChar = (nickName: string | undefined) => {
  if (!nickName) return '';
  return nickName?.charAt(0);
};

const getMemberStatus = (
  status: number | undefined,
  banStatus: string | undefined
) => {
  if (banStatus === '0') return '已禁用';
  if (status === 1) return '正常';
  return '未激活';
};

const getMemberTypeText = (memberType: string | undefined) => {
  const typeMap: Record<string, string> = {
    '1': '普通会员',
    '2': '铂金会员',
    '3': '钻石会员',
  };
  return memberType ? typeMap[memberType] || '普通会员' : '普通会员';
};

const MemberCenterPage = () => {
  const [searchText, setSearchText] = useState('');
  const [memberList, setMemberList] = useState<
    Record<string, BUSINESS.TMemberDetailVo[]>
  >({});
  const [activeTag, setActiveTag] = useState('1');
  const [lastUpdateTime, setLastUpdateTime] = useState('');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastText, setToastText] = useState('');
  const [originalMemberList, setOriginalMemberList] = useState<
    BUSINESS.TMemberDetailVo[]
  >([]);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterCardLevel, setFilterCardLevel] = useState('all');
  const [isActionOpen, setIsActionOpen] = useState(false);
  const [currentActionType, setCurrentActionType] = useState('');
  const [currentMember, setCurrentMember] =
    useState<BUSINESS.TMemberDetailVo | null>(null);
  const [total, setTotal] = useState(0);
  const [letters, setLetters] = useState<string[]>([
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
  ]);
  const fliteList = [
    {
      name: '即将到期',
      id: '1',
    },
    {
      name: '按余额',
      id: '2',
    },
  ];
  const container: React.RefObject<PageContainerRef> = useRef(null);

  useDidShow(() => {
    console.log('useDidShow');
    loadMemberData();
  });

  // 更新当前时间
  const updateCurrentTime = () => {
    const now = new Date();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    setLastUpdateTime(`${month}-${day} ${hours}:${minutes}`);
  };

  // 按首字母处理会员数据
  const processMemberData = (members: any[]) => {
    if (!members || members.length === 0) return {};

    // 按首字母分组
    return members.reduce((acc, member) => {
      const firstLetter = member.firstPinYin || '#';
      if (!acc[firstLetter]) {
        acc[firstLetter] = [];
      }
      acc[firstLetter].push({
        ...member,
        firstPinYin: firstLetter,
      });
      return acc;
    }, {});
  };

  // 加载会员数据
  const loadMemberData = () => {
    setIsRefreshing(true);
    getMemberDetaillist({
      detailVo: {
        pageQueryModel: 0
      } as any,
    })
      .then((response: BUSINESS.RListTMemberDetailVo) => {
        if (response?.total && response?.data) {
          const processedData = processMemberData(response.data);

          const letters = Object.keys(processedData).sort();
          setLetters(letters);
          setMemberList(processedData);
          setOriginalMemberList(response.data);
          setTotal(response.total || 0);
        } else {
          setMemberList({});
          setOriginalMemberList([]);
          setTotal(0);
        }
        updateCurrentTime();
        // setToastText('刷新成功');
        // setShowToast(true);
        // setTimeout(() => setShowToast(false), 1500);
      })
      .catch((err) => {
        console.error('获取会员列表失败', err);
        setToastText('刷新失败');
        setShowToast(true);
        setTimeout(() => setShowToast(false), 1500);
      })
      .finally(() => {
        setIsRefreshing(false);
      });
  };

  // 处理刷新事件
  const handleRefresh = () => {
    if (!isRefreshing) {
      loadMemberData();
    }
  };

  // 处理搜索
  const handleSearch = () => {
    if (!searchText.trim()) {
      loadMemberData();
      return;
    }

    setIsRefreshing(true);
    getMemberDetaillist({
      detailVo: {
        nickName: searchText.trim(),
        phonenumber: searchText.trim(),
      } as any,
    })
      .then((response: BUSINESS.RListTMemberDetailVo) => {
        if (response?.total && response?.data && response.data.length > 0) {
          // 去重
          const uniqueMembers = uniqBy(response.data, 'userId');
          // 按首字母处理
          const processedData = processMemberData(uniqueMembers);
          const letters = Object.keys(processedData);
          setLetters(letters);
          setMemberList(processedData);
          setOriginalMemberList(uniqueMembers);
          setTotal(response.total || 0);
          setToastText(`找到 ${uniqueMembers.length} 位会员`);
        } else {
          setMemberList({});
          setOriginalMemberList([]);
          setTotal(0);
          setToastText('未找到相关会员');
        }
        setShowToast(true);
        updateCurrentTime();
        setTimeout(() => setShowToast(false), 1500);
      })
      .catch((err) => {
        console.error('搜索会员失败', err);
        setToastText('搜索失败');
        setShowToast(true);
        setTimeout(() => setShowToast(false), 1500);
      })
      .finally(() => {
        setIsRefreshing(false);
      });
  };

  const handleAddMember = () => {
    Taro.navigateTo({
      url: '/member/addmember/addmember-page',
    });
  };

  const handleMemberDetail = (id, memberId) => {
    Taro.navigateTo({
      url: `/member/member-detail/member-detail-page?id=${id}&memberId=${memberId}`,
    });
  };

  // 复制手机号到剪贴板
  const copyPhoneNumber = (phone, e) => {
    e.stopPropagation();
    if (!phone) return;

    Taro.setClipboardData({
      data: phone,
      success: () => {
        Taro.showToast({
          title: '手机号已复制',
          icon: 'success',
          duration: 2000,
        });
      },
    });
  };

  // 处理筛选按钮点击
  const handleFilterClick = () => {
    setIsFilterOpen(true);
  };

  // 处理筛选关闭
  const handleFilterClose = () => {
    setIsFilterOpen(false);
  };

  // 应用筛选条件
  const applyFilters = () => {
    let filteredList = [...originalMemberList];

    // 处理状态筛选
    if (filterStatus !== 'all') {
      filteredList = filteredList.filter((member) =>
        filterStatus === 'active'
          ? member.status === 1 || member.banStatus === '1'
          : member.status === 0 || member.banStatus === '0'
      );
    }

    // 处理会员等级筛选
    if (filterCardLevel !== 'all') {
      filteredList = filteredList.filter(
        (member) =>
          member.memberType ===
          (filterCardLevel === '普通会员'
            ? '1'
            : filterCardLevel === '铂金会员'
              ? '2'
              : filterCardLevel === '钻石会员'
                ? '3'
                : '')
      );
    }

    const processedData = processMemberData(filteredList);
    setMemberList(processedData);
    setIsFilterOpen(false);
  };

  // 处理标签筛选
  const handleTagFilter = (tagId) => {
    setActiveTag(tagId);
    let filteredList = [...originalMemberList];

    if (tagId === '1') {
      // 即将到期的逻辑：假设endDate在30天内的为即将到期
      const thirtyDaysLater = new Date();
      thirtyDaysLater.setDate(thirtyDaysLater.getDate() + 30);

      filteredList = filteredList.filter((member) => {
        if (!member.endDate) return false;
        const endDate = new Date(member.endDate);
        return endDate <= thirtyDaysLater && endDate >= new Date();
      });
    } else if (tagId === '2') {
      // 按余额排序（从高到低）
      filteredList.sort((a, b) => {
        const balanceA = a.balance === null ? 0 : Number(a.balance);
        const balanceB = b.balance === null ? 0 : Number(b.balance);
        return balanceB - balanceA;
      });
    }

    const processedData = processMemberData(filteredList);
    setMemberList(processedData);
  };

  // 打开历史记录/消费记录/副卡信息
  const openActionPanel = (actionType, member, e) => {
    e.stopPropagation();
    setCurrentActionType(actionType);
    setCurrentMember(member);
    setIsActionOpen(true);
  };

  // 关闭操作面板
  const closeActionPanel = () => {
    setIsActionOpen(false);
    setCurrentActionType('');
    setCurrentMember(null);
  };

  // 渲染操作面板内容
  const renderActionContent = () => {
    if (!currentMember) return null;

    switch (currentActionType) {
      case 'history':
        return (
          <View className={styles.actionContent}>
            <View className={styles.actionHeader}>
              <Text className={styles.actionTitle}>历史记录</Text>
              <Text className={styles.actionSubtitle}>
                {currentMember.nickName || '无名'}
              </Text>
            </View>
            <View className={styles.actionList}>
              <View className={styles.actionItem}>
                <Text className={styles.actionDate}>2024-04-01 12:30</Text>
                <Text className={styles.actionDesc}>会员卡充值</Text>
                <Text
                  className={
                    styles.actionAmount + ' ' + styles.actionAmountPositive
                  }
                >
                  +¥500.00
                </Text>
              </View>
              <View className={styles.actionItem}>
                <Text className={styles.actionDate}>2024-03-20 15:45</Text>
                <Text className={styles.actionDesc}>消费：精油SPA</Text>
                <Text
                  className={
                    styles.actionAmount + ' ' + styles.actionAmountNegative
                  }
                >
                  -¥299.00
                </Text>
              </View>
              <View className={styles.actionItem}>
                <Text className={styles.actionDate}>2024-03-10 11:20</Text>
                <Text className={styles.actionDesc}>积分兑换</Text>
                <Text
                  className={
                    styles.actionAmount + ' ' + styles.actionAmountNegative
                  }
                >
                  -200积分
                </Text>
              </View>
            </View>
          </View>
        );
      case 'consumption':
        return (
          <View className={styles.actionContent}>
            <View className={styles.actionHeader}>
              <Text className={styles.actionTitle}>消费记录</Text>
              <Text className={styles.actionSubtitle}>
                {currentMember.nickName || '无名'}
              </Text>
            </View>
            <View className={styles.actionList}>
              <View className={styles.actionItem}>
                <Text className={styles.actionDate}>2024-03-20 15:45</Text>
                <Text className={styles.actionDesc}>精油SPA</Text>
                <Text className={styles.actionAmount}>¥299.00</Text>
              </View>
              <View className={styles.actionItem}>
                <Text className={styles.actionDate}>2024-02-15 16:30</Text>
                <Text className={styles.actionDesc}>面部护理</Text>
                <Text className={styles.actionAmount}>¥199.00</Text>
              </View>
              <View className={styles.actionItem}>
                <Text className={styles.actionDate}>2024-01-05 14:20</Text>
                <Text className={styles.actionDesc}>全身按摩</Text>
                <Text className={styles.actionAmount}>¥399.00</Text>
              </View>
            </View>
          </View>
        );
      case 'subcard':
        return (
          <View className={styles.actionContent}>
            <View className={styles.actionHeader}>
              <Text className={styles.actionTitle}>副卡信息</Text>
              <Text className={styles.actionSubtitle}>
                {currentMember.nickName || '无名'}
              </Text>
            </View>
            {Array.isArray(currentMember.subCards) &&
              currentMember.subCards.length > 0 ? (
              <View className={styles.actionList}>
                {currentMember.subCards.map((card, index) => (
                  <View key={index} className={styles.actionItem}>
                    <View className={styles.cardInfo}>
                      <Text className={styles.cardName}>{card.cardName}</Text>
                      <Text className={styles.cardHolder}>
                        {card.holderName}
                      </Text>
                      <Text className={styles.cardRelation}>
                        {card.relation}
                      </Text>
                    </View>
                    <Text className={styles.cardBalance}>
                      余额: ¥{card.balance.toFixed(2)}
                    </Text>
                  </View>
                ))}
              </View>
            ) : (
              <View className={styles.emptyState}>
                <Text>暂无副卡信息</Text>
                <AtButton
                  size="small"
                  type="primary"
                  className={styles.addSubCard}
                >
                  添加副卡
                </AtButton>
              </View>
            )}
          </View>
        );
      default:
        return null;
    }
  };

  return (
    <PageWithNav
      showNavBar
      title="会员管理"
      containerRef={container}
      className={styles.container}
    >
      {showToast && (
        <AtToast
          isOpened={showToast}
          text={toastText}
          icon={
            toastText.includes('成功') || toastText.includes('找到')
              ? 'check'
              : 'close'
          }
          status={
            toastText.includes('成功') || toastText.includes('找到')
              ? 'success'
              : 'error'
          }
          hasMask={false}
        />
      )}
      <View className={styles.searchBar}>
        <View className={styles.searchInputWrapper}>
          <AtIcon
            value="search"
            size="14"
            color="#999"
            className={styles.searchIcon}
          />
          <Input
            placeholder="搜索会员姓名/手机号"
            value={searchText}
            onInput={(e) => setSearchText(e.detail.value)}
            onConfirm={handleSearch}
          />
          {searchText && (
            <AtIcon
              value="close-circle"
              size="14"
              color="#999"
              className={styles.clearIcon}
              onClick={() => {
                setSearchText('');
                loadMemberData();
              }}
            />
          )}
        </View>
        <AtButton
          type="primary"
          className={styles.searchButton}
          onClick={handleSearch}
        >
          搜索会员
        </AtButton>
      </View>

      {/* <View className={styles.tagContainer}>
        <View>
          {fliteList.map((item) => {
            return (
              <AtTag
                key={item.id}
                circle
                className={activeTag === item.id ? styles.activeTag : ''}
                onClick={() => handleTagFilter(item.id)}
              >
                {item.name}
              </AtTag>
            );
          })}
        </View>

        <View className={styles.filterButton} onClick={handleFilterClick}>
          <Text>筛</Text>
          <Image
            src={filterimg}
            style={{ width: 16, height: 16, marginLeft: 4 }}
          />
        </View>
      </View> */}

      <View className={styles.refreshBox}>
        <View className={styles.refresh} onClick={handleRefresh}>
          <Image className={styles.refreshImg} src={refreshImg} />
          <Text style={{ fontSize: 12, color: '#999', marginLeft: 4 }}>
            {isRefreshing ? '刷新中...' : `更新于 ${lastUpdateTime}`}
          </Text>
        </View>
      </View>

      <View className={styles.memberListContainer}>
        <AlphabetIndex
          letters={letters}
          onLetterClick={(letter) => {
            const element = document.getElementById(`member-section-${letter}`);
            if (element) {
              element.scrollIntoView({ behavior: 'smooth' });
            }
          }}
          onLetterSelect={(letter) => {
            const element = document.getElementById(`member-section-${letter}`);
            if (element) {
              element.scrollIntoView({ behavior: 'smooth' });
            }
          }}
          className={styles.alphabetIndex}
        />

        <View className={styles.memberList}>
          {Object.keys(memberList).length === 0 && !isRefreshing ? (
            <View className={styles.emptyState}>
              <Text>暂无会员数据</Text>
            </View>
          ) : (
            Object.entries(memberList)
              .sort()
              .map(([letter, members]: [string, any[]]) => (
                <View
                  key={letter}
                  id={`member-section-${letter}`}
                  className={styles.memberSection}
                >
                  <Text className={styles.sectionTitle}>{letter}</Text>
                  {members.map((member) => (
                    <View
                      key={member.userId}
                      className={styles.memberItem}
                      onClick={() => {
                        memberStore.setMeberDetail(member);
                        handleMemberDetail(member.userId, member.memberId);
                      }}
                    >
                      <View className={styles.memberAvatarCard}>
                        <View className={styles.memberAvatar}>
                          {member.avatar ? (
                            <Image
                              className={styles.avatarImage}
                              src={member.avatar}
                            />
                          ) : (
                            <View className={styles.avatarText}>
                              {getFirstChar(member.nickName)}
                            </View>
                          )}
                        </View>

                        <View className={styles.memberInfo}>
                          <View className={styles.nameRow}>
                            <Text className={styles.memberName}>
                              {member.nickName || '无名'}
                            </Text>
                            <Image
                              style={{ width: 16, height: 16, marginLeft: 5 }}
                              src={member.sex === 1 ? nan : nv}
                            />
                          </View>

                          <View className={styles.birthday}>
                            <Text className={styles.memberbirthday}>
                              <Image
                                src={birthday}
                                style={{
                                  width: 16,
                                  height: 16,
                                  marginRight: 5,
                                }}
                              />
                              {formatDate(member.birthday) ||
                                formatDate(member.registerTime) ||
                                '未设置'}
                            </Text>
                          </View>

                          <View className={styles.phoneRow}>
                            <Text className={styles.memberPhone}>
                              手机号：{member.phonenumber || '未设置'}
                            </Text>
                            {member.phonenumber && (
                              <Image
                                src={fuzhi}
                                className={styles.bottomImg}
                                onClick={(e) =>
                                  copyPhoneNumber(member.phonenumber, e)
                                }
                              />
                            )}
                          </View>

                          <View className={styles.dateRow}>
                            <Text className={styles.createDate}>
                              {member.endDate
                                ? `${formatDate(member.endDate)}到期`
                                : '永久有效'}
                            </Text>
                            <Text>&nbsp;&nbsp;|&nbsp;&nbsp;</Text>
                            <Text className={styles.memberBalance}>
                              余额 ¥{formatBalance(member.balance)}
                            </Text>
                          </View>
                        </View>
                      </View>

                      {/* <View className={styles.actionButtons}>
                        <View
                          className={styles.actionButton}
                          onClick={(e) => openActionPanel('history', member, e)}
                        >
                          <Image src={lishi} className={styles.bottomImg} />
                          <Text className={styles.actionText}>历史记录</Text>
                        </View>
                        <View
                          className={styles.actionButton}
                          onClick={(e) =>
                            openActionPanel('consumption', member, e)
                          }
                        >
                          <Image src={xiaofei} className={styles.bottomImg} />
                          <Text className={styles.actionText}>消费记录</Text>
                        </View>
                        <View
                          className={styles.actionButton}
                          onClick={(e) => openActionPanel('subcard', member, e)}
                        >
                          <Image src={fuka} className={styles.bottomImg} />
                          <Text className={styles.actionText}>副卡信息</Text>
                        </View>
                      </View> */}

                      {/* <View className={styles.memberType}>
                        <Image
                          src={memberImg}
                          style={{ width: 16, height: 14, marginRight: 4 }}
                        />
                        <Text className={styles.memberTypeCard}>
                          {getMemberTypeText(member.memberType)}
                        </Text>
                        {member.banStatus === '0' && (
                          <Text className={styles.memberStatus}>
                            （已禁用）
                          </Text>
                        )}
                      </View> */}
                    </View>
                  ))}
                </View>
              ))
          )}
        </View>
      </View>

      <View className={styles.addButtonContainer}>
        <AtButton
          type="primary"
          className={styles.addButton}
          onClick={handleAddMember}
        >
          录入会员
        </AtButton>
      </View>

      {/* 筛选弹窗 */}
      <AtFloatLayout
        isOpened={isFilterOpen}
        title="筛选条件"
        onClose={handleFilterClose}
      >
        <View className={styles.filterPanel}>
          <View className={styles.filterSection}>
            <Text className={styles.filterSectionTitle}>会员状态</Text>
            <AtRadio
              options={[
                { label: '全部', value: 'all' },
                { label: '启用', value: 'active' },
                { label: '禁用', value: 'inactive' },
              ]}
              value={filterStatus}
              onClick={(value) => setFilterStatus(value)}
            />
          </View>

          <View className={styles.filterSection}>
            <Text className={styles.filterSectionTitle}>会员等级</Text>
            <AtRadio
              options={[
                { label: '全部', value: 'all' },
                { label: '普通会员', value: '普通会员' },
                { label: '铂金会员', value: '铂金会员' },
                { label: '钻石会员', value: '钻石会员' },
              ]}
              value={filterCardLevel}
              onClick={(value) => setFilterCardLevel(value)}
            />
          </View>

          <View className={styles.filterActions}>
            <AtButton onClick={handleFilterClose}>取消</AtButton>
            <AtButton type="primary" onClick={applyFilters}>
              确认
            </AtButton>
          </View>
        </View>
      </AtFloatLayout>

      {/* 操作面板 */}
      <AtFloatLayout
        isOpened={isActionOpen}
        title={
          currentActionType === 'history'
            ? '历史记录'
            : currentActionType === 'consumption'
              ? '消费记录'
              : currentActionType === 'subcard'
                ? '副卡信息'
                : ''
        }
        onClose={closeActionPanel}
      >
        {renderActionContent()}
      </AtFloatLayout>
    </PageWithNav>
  );
};

export default MemberCenterPage;
