import { View, Text, Image, Input, Switch, Picker } from '@tarojs/components';
import { useEffect, useState } from 'react';
import { AtButton, AtIcon, AtList, AtListItem } from 'taro-ui';
import Taro from '@tarojs/taro';
import Form, { Field, useForm } from 'rc-field-form';
import dayjs from 'dayjs';
import styles from './addmember-page.module.scss';
import uploadIcon from '@/assets/image/member/tianjia (2)@2x.png';
import { EmployeeItem } from '@/view/component/employee-selector';
import { getMemCardtemplateTemplateId } from '@/service/business/huiyuankamoban';

import { getEmployeeList } from '@/service/business/businessEmployeeController';
import { postMember } from '@/service/business/huiyuanguanli/postMember';
import PageWithNav from '@/view/component/page-with-nav/page-with-nav.component';
import { omit } from 'lodash-es';

const SOURCE_LIST = {
  1: '上门客人',
  2: '员工带客',
  3: '抖音客户',
};

const AddMemberPage = () => {
  const [form] = useForm();
  const [employeeList, setEmployeeList] = useState<EmployeeItem[]>([]);

  const [sex, setSex] = useState(1);
  const [source, setSource] = useState(1);

  const [staff, setStaff] = useState<EmployeeItem | null>(null);

  const [birthday, setBirthday] = useState('1997-12-31');
  const [expireDate, setExpireDate] = useState(dayjs().format('YYYY-MM-DD'));
  const [shortMessage, setShortMessage] = useState(true);
  // 会员卡
  const [card, setCard] = useState<any>(null);

  const { templateId = null } = Taro.getCurrentInstance().router?.params || {};

  // 处理选择会员卡
  const handleSelectCard = () => {
    Taro.navigateTo({
      url: `/member/select-card/select-card-page?type=addMember`,
    });
  };

  useEffect(() => {
    if (templateId) {
      getMemCardtemplateTemplateId({ templateId: Number(templateId) }).then(
        (res) => {
          console.log('res', res);
          setCard(omit(res, ['updateBy', 'updateTime', 'createBy', 'createTime']));
        }
      );
      form.setFieldsValue({
        templateId: templateId,
      });
    }
  }, [templateId]);

  // 处理性别选择
  const handleGenderSelect = () => {
    Taro.showActionSheet({
      itemList: ['男', '女'],
      success: function (res) {
        setSex(res.tapIndex);
        form.setFieldsValue({ gender: res.tapIndex }); // 手动设置表单值
      },
    });
  };

  // 处理顾客来源选择
  const customerSource = (type) => {
    Taro.showActionSheet({
      itemList: Object.values(SOURCE_LIST),
      success: function (res) {
        if (!res) return;

        setSource(res.tapIndex + 1);
        form.setFieldsValue({
          [type]: res.tapIndex + 1,
        });
      },
      fail: function (res) {
        console.log(res.errMsg);
      },
    });
  };

  // 处理日期选择
  const handleDateSelect = (type, value) => {
    if (type === 'birthday') {
      setBirthday(value);
    } else if (type === 'expireDate') {
      setExpireDate(value);
    }
  };

  // 处理 Picker 选择
  const handlePickerSelect = (type, index) => {
    if (type === 'staff') {
      const current = employeeList[index];
      setStaff(current);
      form.setFieldsValue({
        ...form.getFieldsValue(),
        employeeId: current.employeeId,
      });
    }
  };

  // 处理表单提交
  const handleSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        console.log('表单数据:', {
          ...card,
          ...values,
          templateId: card.templateId || templateId || card?.id,
          cardLevel: card.cardLevel || '1',
          operationType: '1',
        });
        postMember({
          memberId: undefined, // 会员id
          ...values,
          ...card,
          templateId: card.templateId || templateId || card?.id,
          cardLevel: card.cardLevel || '1',
          operationType: '1',
        })
          .then((res) => {
            console.log('添加会员成功:', res);
            Taro.showToast({
              title: '添加会员成功',
              icon: 'success',
              duration: 2000,
            });
            setTimeout(() => {
              Taro.navigateBack();
            }, 2000);
          })
          .catch((err) => {
            if (err.data.msg.includes('该会员号码')) {
              Taro.showToast({
                title: '该手机号已注册为会员',
                icon: 'none',
                duration: 2000,
              });
            } else {
              Taro.showToast({
                title: '添加会员失败',
                icon: 'none',
                duration: 2000,
              });
            }
          });
      })
      .catch((errorInfo) => {
        console.log('表单验证失败:', errorInfo);

        const error = errorInfo.errorFields[0].errors[0];
        Taro.showToast({
          title: error,
          icon: 'none',
          duration: 2000,
        });
      });
  };

  useEffect(() => {
    // 获取员工列表
    getEmployeeList({} as any).then((res) => {
      if (res?.data) {
        setEmployeeList(
          res.data.map((item) => ({
            employeeId: item.employeeId,
            employeeNickname: item.employeeNickname,
            avatarUrl: item.avatarUrl,
            position: item.position,
          }))
        );
      }
    });

    // 监听会员卡选择
    const handler = (data) => {
      console.log('事件触发数据:', data);
      if (data.card) {
        setCard(data.card);
        form.setFieldsValue({
          templateId: data.card?.templateId,
          amount: data.card?.retailPrice || 0,
        });
      }
    };
    Taro.eventCenter.on('UPDATE_CARD', handler);

    return () => {
      console.log('移除 UPDATE_CARD 监听');
      Taro.eventCenter.off('UPDATE_CARD', handler);
    };
  }, []);

  return (
    <PageWithNav title="添加会员">
      <View className={styles.container}>
        <Form form={form}>
          <>
            <View className={styles.card} onClick={handleSelectCard}>
              {card ? (
                <View
                  key={card.templateId}
                  className={styles.cardItem}
                  style={{ backgroundColor: card.color }}
                >
                  <View
                    className={styles.cardHeader}
                    style={{ backgroundColor: card.backgroundColor }}
                  >
                    <Text className={styles.cardName}>{card.cardType}</Text>
                  </View>
                  <View className={styles.cardContent}>
                    <Text className={styles.cardDiscount}>
                      享受折扣：{card.cardName}
                    </Text>
                    <Text className={styles.cardProject}>赠送项目：-</Text>
                  </View>
                  <View className={styles.cardBackground}>
                    <View className={styles.cardPattern}></View>
                  </View>
                </View>
              ) : (
                <View className={styles.cardSelect}>
                  <Image src={uploadIcon} className={styles.selectIcon} />
                  <Text className={styles.selectText}>
                    点击选择要办理的会员卡
                  </Text>
                </View>
              )}
              {/* 隐藏的 Field 只用于存储值 */}
              <Field
                name="templateId"
                rules={[{ required: true, message: '请选择会员卡' }]}
              >
                <Input style={{ display: 'none' }} />
              </Field>
            </View>
          </>

          <View className={styles.module}>
            <View className={styles.formItem}>
              <Text className={styles.label}>
                姓名<Text className={styles.required}>*</Text>
              </Text>
              <Field
                name="realName"
                rules={[{ required: true, message: '请输入会员姓名' }]}
              >
                {(control) => (
                  <Input
                    className={styles.input}
                    placeholder="请输入会员姓名"
                    onInput={(e) => {
                      control.onChange(e.detail.value);
                    }}
                  />
                )}
              </Field>
            </View>

            <View className={styles.formItem}>
              <Text className={styles.label}>
                手机号<Text className={styles.required}>*</Text>
              </Text>
              <Field
                name="phonenumber"
                rules={[
                  { required: true, message: '请输入手机号码' },
                  { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' },
                ]}
              >
                {(control) => (
                  <Input
                    className={styles.input}
                    placeholder="请输入手机号码"
                    type="number"
                    maxlength={11}
                    placeholderClass={styles.placeholder}
                    onInput={(e) => {
                      control.onChange(e.detail.value);
                    }}
                  />
                )}
              </Field>
            </View>

            <View className={styles.formItem} onClick={handleGenderSelect}>
              <Text className={styles.label}>
                性别<Text className={styles.required}>*</Text>
              </Text>
              <View className={styles.selectValue}>
                <Text>{sex === 0 ? '男' : '女'}</Text>
                <AtIcon value="chevron-right" size="16" color="#ccc" />
              </View>
              {/* 隐藏的 Field 只用于存储值 */}
              <Field name="sex" initialValue={sex}>
                <Input style={{ display: 'none' }} />
              </Field>
            </View>

            <View
              className={styles.formItem}
              onClick={() => customerSource('source')}
            >
              <Text className={styles.label}>
                顾客来源<Text className={styles.required}>*</Text>
              </Text>
              <View className={styles.selectValue}>
                <Text>{SOURCE_LIST[source]}</Text>
                <AtIcon value="chevron-right" size="16" color="#ccc" />
              </View>
              <Field name="source" initialValue={source}>
                <Input style={{ display: 'none' }} />
              </Field>
            </View>
          </View>
          <View className={styles.divider}></View>
          <View className={styles.module}>
            <View className={styles.formItem}>
              <Text className={styles.label}>昵称</Text>
              <Field name="nickName">
                {(control) => (
                  <Input
                    className={styles.input}
                    placeholder="请输入会员昵称"
                    placeholderClass={styles.placeholder}
                    onInput={(e) => {
                      control.onChange(e.detail.value);
                    }}
                  />
                )}
              </Field>
            </View>

            {/* 修改为这种形式 */}
            <View className={styles.formItem}>
              <Picker
                mode="date"
                onChange={(e) => handleDateSelect('birthday', e.detail.value)}
                className={styles.piacker}
              >
                <AtList className={styles.piackerContent}>
                  <Text className={styles.label}>生日</Text>
                  <View className={styles.selectValue}>
                    <Text>{birthday}</Text>
                    <AtIcon value="chevron-right" size="16" color="#ccc" />
                  </View>
                </AtList>
              </Picker>
              {/* 隐藏的 Field 只用于存储值 */}
              <Field name="birthday" initialValue={birthday}>
                <Input style={{ display: 'none' }} />
              </Field>
            </View>

            <View className={styles.formItem}>
              <Picker
                mode="selector"
                range={employeeList.map((item) => item.employeeNickname)}
                onChange={(e) => handlePickerSelect('staff', e.detail.value)}
                className={styles.piacker}
              >
                <AtList className={styles.piackerContent}>
                  <Text className={styles.label}>所属员工</Text>
                  <View className={styles.selectValue}>
                    <Text>{staff?.employeeNickname || '请选择'}</Text>
                    <AtIcon value="chevron-right" size="16" color="#ccc" />
                  </View>
                </AtList>
              </Picker>
              {/* <Text className={styles.label}>所属员工</Text>
              <View className={styles.selectValue}>
                <Text>{staff}</Text>
                <AtIcon value="chevron-right" size="16" color="#ccc" />
              </View> */}
              <Field name="employeeId" initialValue={staff?.employeeId}>
                <Input style={{ display: 'none' }} />
              </Field>
            </View>

            {/* <View className={styles.formItem}>
              <Text className={styles.label}>顾客标签</Text>
              <Field name="staffRemark">
                <Input
                  className={styles.input}
                  placeholder="请会员备注"
                  placeholderClass={styles.placeholder}
                />
              </Field>
            </View> */}

            <View className={styles.formItem}>
              <Text className={styles.label}>备注</Text>

              <View className={styles.remarkBox}>
                <Field name="remarks">
                  {(control) => (
                    <Input
                      className={styles.input}
                      placeholder="请输入备注，例如：张三推荐、微信支付，字数不超过200字"
                      onInput={(e) => {
                        control.onChange(e.detail.value);
                      }}
                    />
                  )}
                </Field>
              </View>
            </View>
          </View>
          <View className={styles.divider}></View>
          <View className={styles.module}>
            <View className={styles.formItem}>
              <Text className={styles.label}>实收金额</Text>
              <View className={styles.amountContainer}>
                <Field name="amount" initialValue={0}>
                  {(control) => (
                    <Input
                      className={styles.amountInput}
                      type="digit"
                      placeholderClass={styles.placeholder}
                      onInput={(e) => {
                        control.onChange(e.detail.value);
                      }}
                    />
                  )}
                </Field>
                <Text className={styles.unit}>元</Text>
              </View>
            </View>

            <View className={styles.formItem}>
              <Picker
                mode="date"
                onChange={(e) => handleDateSelect('expireDate', e.detail.value)}
                className={styles.piacker}
              >
                <AtList className={styles.piackerContent}>
                  <Text className={styles.label}>添加时间</Text>
                  <View className={styles.selectValue}>
                    <Text>{expireDate}</Text>
                    <AtIcon value="chevron-right" size="16" color="#ccc" />
                  </View>
                </AtList>
              </Picker>

              <Field name="expireDate" initialValue={expireDate}>
                <Input style={{ display: 'none' }} />
              </Field>
            </View>

            <View className={styles.formItem}>
              <View className={styles.shortMessage}>
                <Text className={styles.label}>短信通知</Text>
                <Switch
                  color="#ffb400"
                  className={styles.switch}
                  checked={shortMessage}
                  onChange={(e) => {
                    setShortMessage(e.detail.value);
                    form.setFieldsValue({
                      isSmsSubscribe: Number(e.detail.value),
                    });
                  }}
                />
              </View>
              <Field name="isSmsSubscribe" initialValue={1}>
                <Input style={{ display: 'none' }} />
              </Field>
            </View>
          </View>
        </Form>
      </View>
      <View className={styles.submitBtnContainer}>
        <AtButton
          type="primary"
          className={styles.submitBtn}
          formType="submit"
          onClick={handleSubmit}
        >
          确认添加
        </AtButton>
      </View>
    </PageWithNav>
  );
};

export default AddMemberPage;
