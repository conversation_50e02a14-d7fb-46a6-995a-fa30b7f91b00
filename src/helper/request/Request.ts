import Taro from "@tarojs/taro";
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import { sha256 } from "js-sha256";
import { isRefreshRequest, refreshToken } from "./refresh";

export type RequestConfigs = AxiosRequestConfig & {
	useOriginData?: boolean;
	// 触发立即更新
	flushApiHook?: boolean;
};

interface ErrorInfo {
	code: number;
	message: string;
}

type RequestResult<T, U> = U extends { useOriginData: true }
	? T
	: T extends { data?: infer D }
	? D
	: never;

class Request {
	private instance: AxiosInstance;

	private store: Map<string, Array<Promise<any> | null>>;

	private options: AxiosRequestConfig;

	constructor(options: AxiosRequestConfig) {
		this.instance = axios.create(options);

		this.options = options;
		this.store = new Map();
		this.interceptorRequest();
		this.interceptorResponse();
	}

	private interceptorRequest() {
		this.instance.interceptors.request.use(
			(config) => {
				return config;
			},
			(error) => {
				console.log("intercept request error", error);
				Promise.reject(error);
			}
		);
	}

	private interceptorResponse() {
		this.instance.interceptors.response.use(
			async (response): Promise<any> => {
				// 对响应数据做处理，以下根据实际数据结构改动！！...
				const [checked, errorInfo, overrideResData] = await this.checkStatus(response);

				if (!checked) {
					// Taro.atMessage({
					// 	message: errorInfo?.message ?? "请求失败",
					// 	type: "error",
					// });
					Taro.showToast({
						title: "服务器出错",
						icon: "error",
						duration: 2000,
					});
					return Promise.reject(response);
				}

				// 覆盖原响应数据
				if (checked && overrideResData) {
					return Promise.resolve(overrideResData);
				}

				if (response) {
					return Promise.resolve(response.data);
				}
			},
			async (error) => {
				console.log("request error", error);
				if (error.message.indexOf("timeout") !== -1) {
					return Promise.reject({
						message: "请求超时",
					});
				}
				const [checked] = await this.checkStatus(error.response);
				return Promise.reject(error.response);
			}
		);
	}

	private async checkStatus(
		response: AxiosResponse<any>
	): Promise<[boolean] | [boolean, ErrorInfo] | [boolean, undefined, AxiosResponse<any>]> {
		const { code, msg } = response?.data || {};
		const { status } = response || {};

		if (!status) {
			return [false];
		}

		// 令牌过期时尝试刷新令牌
		if (code === 401 && !isRefreshRequest(response.config)) {
			// 刷新token
			const refreshResult = await refreshToken();
			if (refreshResult) {
				// 用新获取的token更新请求头中的token
				const loginInfo = Taro.getStorageSync("loginInfo")
				response.config.headers!.Authorization = `Bearer ${loginInfo.token}`
				// 重新发起请求
				const newRes = await this.instance.request(response.config);
				return [true, undefined, newRes]
			} else {
				// token刷新失败
				const pages = Taro.getCurrentPages();
				const lastPage = pages[pages.length - 1];
				// 处理重复跳转到登录页的情况
				if (!lastPage || lastPage.route != "view/page/single/login/login-page") {
					Taro.reLaunch({
						url: "/view/page/single/login/login-page",
					});
				}

				return [false];
			}
		}

		// 单地登录判断，弹出不同提示
		if (status === 200) {
			if (code === 200) {
				return [true];
			} else {
				return [false, { code, message: msg }];
			}
		}

		return [false, { code: status, message: "请求失败" }];
	}

	private handleFinish(key: string, index: number) {
		const promises = this.store.get(key);
		if (promises?.filter((item) => item).length === 1) {
			this.store.delete(key);
		} else if (promises && promises[index]) {
			promises[index] = null;
		}
	}

	private async handleRequest(config: any) {
		const hash = sha256.create();
		hash.update(
			JSON.stringify({
				params: config.params,
				data: config.data,
				url: config.url,
				method: config.method,
			})
		);

		const fetchKey = hash.hex().slice(0, 40);
		const promises = this.store.get(fetchKey);
		const index = promises?.length || 0;
		let promise = promises?.[0];
		// const controller = new AbortController();

		// if (config.signal) {
		// 	config.signal.onabort = (reason: any) => {
		// 		const _promises = this.store
		// 			.get(fetchKey)
		// 			?.filter((item) => item);
		// 		if (_promises?.length === 1) {
		// 			controller.abort(reason);
		// 			this.handleFinish(fetchKey, index);
		// 		}
		// 	};
		// }

		if (!promise) {
			promise = this.instance({
				...config,
				// signal: controller.signal,
				headers: {
					...config.headers,
					fetchKey,
				},
			})
				.catch((error) => {
					// 失败的话，立即删除，可以重试
					this.handleFinish(fetchKey, index);
					return { error };
				})
				.finally(() => {
					// 留50ms缓冲
					setTimeout(() => {
						this.handleFinish(fetchKey, index);
					}, 50);
				});
		}

		const newPromise = Promise.resolve(promise).then((result: any) => {
			// if (config.signal?.aborted) {
			// 	this.handleFinish(fetchKey, index);
			// 	return result;
			// }
			return result;
		});
		this.store.set(fetchKey, [...(promises || []), newPromise]);
		return newPromise;
	}

	// add override type
	public async request<T = unknown, U extends RequestConfigs = {}>(
		url: string,
		config: U
	): Promise<RequestResult<T, U> | null> {
		// todo
		const options = {
			url,
			...config,
			headers: {
				...(config?.headers ?? {}),
			},
		};

		const res = await this.handleRequest(options);

		if (!res) {
			return null;
		}

		if (res.error) {
			console.log(res.error, 4444)
			// throw new Error(res.error);
			throw res.error
		}

		if (config.useOriginData) {
			return res;
		}

		return res.data;
	}
}

export type IRequest = <T = unknown, U extends RequestConfigs = {}>(
	url: string,
	config: U
) => Promise<RequestResult<T, U> | null>;

export default Request;
