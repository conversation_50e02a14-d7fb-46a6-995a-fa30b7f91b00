import Taro from "@tarojs/taro";
import request from "./instance";

let promise: Promise<Boolean> | null;

export async function refreshToken() {
    if (promise) {
        return promise;
    }

    promise = new Promise(async (resolve) => {
        console.log("刷新token")
        const loginInfo = Taro.getStorageSync("loginInfo")
        if (!loginInfo || !loginInfo.token) {
            resolve(false);
            return
        }
        try {
            const resp: any = await request.request("/system/auth/refresh", {
                method: "POST",
                headers: {
                    Authorization: `Bearer ${loginInfo.token}`,
                },
                __isRefreshToken: true
            })

            // Taro.setStorageSync("loginInfo", {...loginInfo, token: "eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6IjhjMTU4MjdlLTg2ZjQtNDI0MS04Y2EzLTVmMjE5ZDVhMGUxYyIsInVzZXJuYW1lIjoiYWRtaW4ifQ.VjVJ50gwBh4RwFQRamqG8-sRywFMcNVcl3w7FluMcY3pHRdJ3L_q5E_Ji0_DlO_9y-8_ZG2658SF1MBSzBusMg"})
            // resolve(true)

            if (resp) {
                console.log(resp);
                if (resp.token) {
                    Taro.setStorageSync("loginInfo", { ...loginInfo, token: resp.token })
                }
                resolve(true)
            } else {
                resolve(false)
            }
        } catch (error) {
            console.log("刷新token失败")
            resolve(false)
        }

    })

    promise.finally(() => {
        promise = null;
    })

    return promise;
}

export function isRefreshRequest(config: any) {
    return !!config.__isRefreshToken;
}