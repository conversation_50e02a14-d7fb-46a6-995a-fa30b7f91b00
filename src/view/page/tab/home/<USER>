import React, { useState, useEffect } from "react";
import Taro, { useDidShow } from "@tarojs/taro";
import { View, Text, Image, ScrollView } from "@tarojs/components";
import { AtButton, AtGrid, AtTabs, AtTabsPane } from "taro-ui";
import { getDashboardRevenue, getDashboardTraffic } from '@/service/business/dashboardController';
import { postReservationRecordList } from '@/service/business/reservationRecordController';
import { getShop } from '@/service/business/businessController';
import styles from "./home-tab.module.scss";
import ReservationItem from "./reservation-item.component";
import { GlobalInfoStore } from "@/store/global-info.store";
import useStore from "@/hook/store";
import useWindowArea from "@/hook/windowArea";



const HomeTab: React.FC = () => {
	const cache: GlobalInfoStore = useStore().globalInfoStore;
	const { bottomArea } = useWindowArea();

	const [tabbarHeight, setTabbarHeight] = useState(0);
	const [todayData, setTodayData] = useState({
		visitCount: 0,
		turnover: 0,
		newMembers: 0,
		reservationCount: 0,
		conversionRate: 0,
		targetAmount: 0
	});

	const [reservationList, setReservationList] = useState<any[]>([]);
	const [loading, setLoading] = useState(false);
	const [current, setCurrent] = useState(0);
	const [shopName, setShopName] = useState('');

	const [showLogout, setShowLogout] = useState(false)

	useEffect(() => {
		const systemInfo = Taro.getSystemInfoSync();

		const statusBarHeight = systemInfo.statusBarHeight || 0;

		// 获取胶囊信息
		const { width, height, left, top, right } = Taro.getMenuButtonBoundingClientRect();

		// 计算标题栏高度
		const titleBarHeight = height + (top - statusBarHeight) * 2;

		// 计算导航栏高度
		setTabbarHeight(titleBarHeight);

		// const fetchDashboardData = async () => {
		// 	try {
		// 		const [trafficResponse, revenueResponse] = await Promise.all([
		// 			getDashboardTraffic(),
		// 			getDashboardRevenue()
		// 		]);

		// 		setTodayData({
		// 			visitCount: trafficResponse?.guestCount || 0,
		// 			turnover: revenueResponse?.spendingAmount || 0,
		// 			newMembers: trafficResponse?.memberCount || 0,
		// 			reservationCount: trafficResponse?.nonMemberCount || 0,
		// 			conversionRate: revenueResponse?.conversionRate || 0,
		// 			targetAmount: revenueResponse?.depositAmount || 0
		// 		});
		// 	} catch (error) {
		// 		console.error('获取仪表盘数据失败:', error);
		// 	}
		// };

		const fetchShopName = async () => {
			try {
				const res: any = await getShop();
				if (res) {
					setShopName(res.businessName || '');
					//!temporary 目前登录后仅拿到商铺id,暂时先通过接口获取商铺名称并记录到缓存
					cache.setBusinessName(res.businessName || "");
					cache.setBusinessId(res.businessId || undefined)
					cache.setShopInfo(res || {})
				}
			} catch (e) {
				setShopName('');
			}
		};

		const loginInfo = Taro.getStorageSync('loginInfo');
		console.log('loginInfo:', loginInfo);
		fetchShopName();
		// fetchDashboardData();
		fetchReservationList(current);
	}, []);

	const fetchDashboardData = async () => {
		try {
			const [trafficResponse, revenueResponse] = await Promise.all([
				getDashboardTraffic(),
				getDashboardRevenue()
			]);

			setTodayData({
				visitCount: trafficResponse?.guestCount || 0,
				turnover: revenueResponse?.spendingAmount || 0,
				newMembers: trafficResponse?.memberCount || 0,
				reservationCount: trafficResponse?.nonMemberCount || 0,
				conversionRate: revenueResponse?.conversionRate || 0,
				targetAmount: revenueResponse?.depositAmount || 0
			});
		} catch (error) {
			console.error('获取仪表盘数据失败:', error);
		}
	}

	useDidShow(() => {
		fetchDashboardData();
	});

	const gridData = [
		{
			image: require("@/assets/image/home/<USER>"),
			value: "店铺管理",
			link: "/shop/shop-management/shop-management-page",
		},
		{
			image: require("@/assets/image/home/<USER>"),
			value: "预约管理",
			link: "/reservation/reservation-management/reservation-management-page",
		},
		{
			image: require("@/assets/image/home/<USER>"),
			value: "会员管理",
			link: "/member/member-center/member-center-page",
		},
		{
			image: require("@/assets/image/home/<USER>"),
			value: "会员卡模板",
			link: "/member-card-template/template-list/template-list-page",
		},
		{
			image: require("@/assets/image/home/<USER>"),
			value: "员工管理",
			link: "/employee/staff-management/staff-management-page",
		},
		{
			image: require("@/assets/image/home/<USER>"),
			value: "散客记账",
			link: "/view/page/single/walkIn-bill-add/walkIn-bill-add-page",
		},
		{
			image: require("@/assets/image/home/<USER>"),
			value: "客资信息",
			link: "/customer-resources/customer-center/customer-center-page",
		},
	];


	const handleGridClick = (item) => {
		if (item.link) {
			Taro.navigateTo({ url: item.link });
		}
	};

	const scanVerify = async () => {
		try {
			// 调用扫码接口
			const res = await Taro.scanCode({
				scanType: ['qrCode'],
				onlyFromCamera: true
			});

			if (res.result) {
				// 跳转到核销详情页面，传递扫码结果
				Taro.navigateTo({
					url: `/member/member-billing-detail/member-billing-detail-page?scanResult=${encodeURIComponent(res.result)}`
				});
			}
		} catch (error) {
			console.error('扫码失败:', error);
			Taro.showToast({
				title: '扫码失败，请重试',
				icon: 'none'
			});
		} finally {
			const memberId = 14;
			// 跳转到核销详情页面，传递扫码结果
			Taro.navigateTo({
				url: `/member/member-billing-detail/member-billing-detail-page?scanResult=${encodeURIComponent(memberId)}`
			});
		}
	}

	const fetchReservationList = async (tabIndex) => {
		setLoading(true);
		let statuses;
		if (tabIndex === 1) statuses = [1]; // 已预约
		else if (tabIndex === 2) statuses = [0]; // 未预约（待接单）
		// 0:待接单，1:已接单，2:已取消，3:已到店，4:未到店，5:已完成，6:已逾期

		try {
			const res = await postReservationRecordList({ statuses });
			setReservationList(res ?? []);
		} catch (e) {
			setReservationList([]);
		}
		setLoading(false);
	};

	useEffect(() => {
		fetchReservationList(current);
	}, [current]);

	const handleTabClick = (value) => {
		setCurrent(value);
	};

	const tabList = [{ title: '全部' }, { title: '已接单' }, { title: '未接单' }]
	const handleRegisterClick = () => {
		Taro.navigateTo({
			url: '/shop/register-business/register-business-page'
		});
	};

	console.log(reservationList, '---reservationListreservationList')

	const refreshReservationList = () => {
		fetchReservationList(current);
	};

	const { run: logout } = useRequest(() => service.auth.tokenController.logout(), {
		manual: true,
		onBefore: () => Taro.showLoading({ title: "正在退出登录" }),
		onSuccess: () => {
			setShowLogout(false);
			Taro.reLaunch({ url: "/view/page/single/login/login-page" })
		},
		onFinally: () => Taro.hideLoading()
	})

	return (
		<View className={styles.container} >
			<ScrollView className={styles.scrollView} scrollY>
				<View className={styles.bg}></View>
				<View className={styles.homeWrapper}>
					<View className={styles.homeInfoSection} style={`padding-top: ${Taro.getSystemInfoSync().statusBarHeight}px`}>
						<View className={styles.homeInfoHeader} style={`height: ${tabbarHeight}px`}>
							<View className={styles.homeHeaderIconWrapper}>
								<Image
									src={require("@/assets/image/home/<USER>")}
									className={styles.homeHeaderIcon}
								/>
								<Text className={styles.homeHeaderText}>商家中心</Text>
							</View>
						</View>

					</View>

					{/* 头部店铺信息 */}
					<View className={styles.shopHeader} onClick={() => setShowLogout(true)}>
						<Image
							src={require("@/assets/image/home/<USER>")}
							className={styles.shopHeaderIcon}
						/>
						<Text className={styles.shopName}>{shopName}</Text>
						{/* <Image
							src={require("@/assets/image/home/<USER>")}
							className={styles.downIcon}
						/> */}
						{/* <Image
							src={require("@/assets/image/home/<USER>")}
							className={styles.searchIcon}
						/> */}
					</View>


					<View className={styles.shopCenter}>
						{/* 今日数据 */}
						<View className={styles.todayStats}>
							<View className={styles.statsTitle}>
								<Text>今日数据</Text>
								<Text className={styles.statsTime}>统计今日00:00到当前的数据</Text>
							</View>

							<View className={styles.mainStats}>

								<View className={`${styles.statsItem} ${styles.blue}`}>
									<Image
										src={require("@/assets/image/home/<USER>")}
										className={styles.statsIcon}
									/>
									<View>
										<View className={styles.statsLabel}>今日到访数</View>
										<View className={styles.statsValue}>{todayData.visitCount}
											<Text className={styles.statsValueUnit}>人</Text>
										</View>
									</View>
								</View>
								<View className={`${styles.statsItem} ${styles.pink}`}>
									<Image
										src={require("@/assets/image/home/<USER>")}
										className={styles.statsIcon}
									/>
									<View>
										<View className={styles.statsLabel}>今日成交额</View>
										<View className={styles.statsValue}>{todayData.turnover}
											<Text className={styles.statsValueUnit}>元</Text>
										</View>
									</View>
								</View>
							</View>

							<View className={styles.subStats}>
								<View className={styles.subStatsItem}>
									<View className={styles.subLabel}>新增会员</View>
									<View className={styles.subValue}>{todayData.newMembers}
										<Text className={styles.subValueUnit}>人</Text>
									</View>
								</View>
								<View className={styles.subStatsItem}>
									<View className={styles.subLabel}>预约数</View>
									<View className={styles.subValue}>{todayData.reservationCount}
										<Text className={styles.subValueUnit}>人</Text>
									</View>
								</View>
								<View className={styles.subStatsItem}>
									<View className={styles.subLabel}>新客转化率</View>
									<View className={styles.subValue}>{todayData.conversionRate}
										<Text className={styles.subValueUnit}>%</Text>
									</View>
								</View>
								<View className={styles.subStatsItem}>
									<View className={styles.subLabel}>剩余金额</View>
									<View className={styles.subValue}>{todayData.targetAmount}
										<Text className={styles.subValueUnit}>元</Text>
									</View>
								</View>
							</View>

							<View className={styles.statMore}>
								{/* <Image
									src={require("@/assets/image/home/<USER>")}
									className={styles.statDownIcon}
								/> */}
							</View>


						</View>
						<AtButton type='primary' className={styles.scanVerify} onClick={scanVerify}>
							<Image
								className={styles.scanVerifyImg}
								src={require("@/assets/image/home/<USER>")}
							/>
							扫码核销
						</AtButton>

						{/* 功能菜单 */}
						<AtGrid
							data={gridData}
							onClick={handleGridClick}
							columnNum={4}
							hasBorder={false}
						/>

						{/* <View className={`at-row ${styles.businessRegisterDiv}`}>
							<Image onClick={handleRegisterClick}
								className={styles.businessRegisterImg}
								src={require("@/assets/image/register-business.png")}
							/>
						</View> */}

					</View>
					<AtTabs current={current} tabList={tabList} onClick={handleTabClick}>
						<AtTabsPane current={current} index={0}>
							<ScrollView className={styles.reservationList} scrollY style={{ height: '400px' }}>
								{loading ? (
									<View>加载中...</View>
								) : (
									reservationList.map(item => (
										<ReservationItem key={item.id} item={item} onRefresh={refreshReservationList} />
									))
								)}
							</ScrollView>
						</AtTabsPane>
						<AtTabsPane current={current} index={1}>
							<ScrollView className={styles.reservationList} scrollY style={{ height: '400px' }}>
								{loading ? (
									<View>加载中...</View>
								) : (
									reservationList.map(item => (
										<ReservationItem key={item.id} item={item} onRefresh={refreshReservationList} />
									))
								)}
							</ScrollView>
						</AtTabsPane>
						<AtTabsPane current={current} index={2}>
							<ScrollView className={styles.reservationList} scrollY style={{ height: '400px' }}>
								{loading ? (
									<View>加载中...</View>
								) : (
									reservationList.map(item => (
										<ReservationItem key={item.id} item={item} onRefresh={refreshReservationList} />
									))
								)}
							</ScrollView>
						</AtTabsPane>
					</AtTabs>
				</View>
			</ScrollView>
			<View className={`${styles.logoutActionSheet} ${showLogout ? styles.show : ''}`} onClick={() => setShowLogout(false)}>
				<View className={styles.actionPanel} style={{ bottom: bottomArea }}>
					<View className={styles.logoutBtn} onClick={e => { e.stopPropagation(); logout() }} >
						退出登录
					</View>
				</View>
			</View>
		</View>
	);
};

export default HomeTab;

