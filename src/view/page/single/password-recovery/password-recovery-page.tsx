import { View, Input, Button, Label, RichText, Image } from "@tarojs/components";
import Taro from "@tarojs/taro";
import useWindowArea from "@/hook/windowArea";
import React, { useMemo, useRef, useState } from "react";
import { validatePhone } from "@/util/validate-util";
import { AtIcon } from "taro-ui";

const COUNTDOWN = 60;

const PasswordRecoveryPage: React.FC = () => {
  const { bottomArea, topArea, navArea } = useWindowArea()

  const [recoveryForm, setRecoveryForm] = useState({
    username: "",
    verifyCode: ""
  });

  const [showSend, setShowSend] = useState(true);
  const [countdown, setCountdown] = useState(COUNTDOWN);

  const countdownRef = useRef(COUNTDOWN)
  const timer = useRef<NodeJS.Timeout>();

  // 更新找回表单state信息
  const updateForm = (params: Record<string, string>) => {
    setRecoveryForm(state => ({
      ...state,
      ...params
    }))
  }

  // const phoneNumber = useMemo(() => {
  // 	console.log(recoveryForm.username.replace(/\s/g, '').replace(/(\d{3})(\d{0,4})(\d{0,4})/, '$1 $2 $3'))
  // 	return recoveryForm.username.replace(/^(.{3})(.*)(.{4})$/, '$1 $2 $3')
  // }, [recoveryForm.username])

  // const onPhoneNumberChange = (val: string) => {
  // 	updateForm({ username: val.replace(/[^\d]/g, "") })
  // }

  const sendVerifyCode = async () => {
    if (!recoveryForm.username.trim()) {
      Taro.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }

    if (!validatePhone(recoveryForm.username)) {
      Taro.showToast({
        title: '请输入正确的手机号码',
        icon: 'none'
      });
      return;
    }

    try {
      service.auth.tokenController.postAuthSendSmsCode({
        phonenumber: recoveryForm.username
      }).then(res => {
        setShowSend(false);
        Taro.showToast({
          title: '获取成功，请查收短信验证码',
          icon: 'none'
        })
        timer.current = setInterval(() => {
          setCountdown(--countdownRef.current);
          if (countdownRef.current === 0) {
            setShowSend(true)
            clearInterval(timer.current);
            countdownRef.current = COUNTDOWN;
          }
        }, 1000)
      })
    } catch (error) {
      Taro.showToast({
        title: '获取验证码失败',
        icon: 'none'
      })
    }
  }

  /**
   * 账号密码登录
   * @returns 
   */
  const nextStep = async () => {
    if (!recoveryForm.username.trim()) {
      Taro.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }

    if (!validatePhone(recoveryForm.username)) {
      Taro.showToast({
        title: '请输入正确的手机号码',
        icon: 'none'
      });
      return;
    }

    if (!recoveryForm.verifyCode.trim()) {
      Taro.showToast({
        title: '请输入验证码',
        icon: 'none'
      })
      return
    }

    try {
      // todo 调用验证接口

    } catch (error) {

    }
  }

  /**
   * 验证码登录
   */
  const loginWithVerityCode = async () => {
    if (!recoveryForm.username.trim()) {
      Taro.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }

    if (!validatePhone(recoveryForm.username)) {
      Taro.showToast({
        title: '请输入正确的手机号码',
        icon: 'none'
      });
      return;
    }

    if (!recoveryForm.verifyCode.trim()) {
      Taro.showToast({
        title: '请输入验证码',
        icon: 'none'
      })
      return
    }

    try {
      const res = await service.auth.tokenController.postLoginSmsBusiness({
        phonenumber: recoveryForm.username,
        smscode: recoveryForm.verifyCode
      })
      setLoginInfo(res?.access_token);

      Taro.navigateTo({ url: "/view/page/tab/home/<USER>" });

    } catch (error) {
      Taro.showToast({
        title: '登录失败',
        icon: 'error'
      })
    }
  }

  const setLoginInfo = (token) => {
    let loginInfo: any = {};
    loginInfo.token = token;
    Taro.setStorageSync("loginInfo", loginInfo);
    service.business.businessController.getIdsUserId().then((res) => {
      if (res && res.length > 0) {
        // TODO 先默认取第一个
        loginInfo.businessId = res[0];
        Taro.setStorageSync("loginInfo", loginInfo);
      }
    })
  }

  return (
    <View className="w-full h-full flex flex-col bg-cover bg-center overflow-hidden box-border"
      style={{
        backgroundImage: `url(${require("@/assets/image/login/bg.png")})`,
        paddingBottom: bottomArea
      }}>
      {/* 导航栏 */}
      <View className="w-full relative flex items-center justify-center"
        style={{ height: navArea + topArea, paddingTop: topArea }}>
        <View className="absolute left-3 text-base" onClick={() => Taro.navigateBack()}>
          <AtIcon value="chevron-left" size="13" color="#000000" />
        </View>
      </View>

      {/* 顶部欢迎区域 */}
      <View className="h-[265px] relative">
        <View className="absolute bottom-[45px] left-6 text-[28px] font-bold">
          <RichText nodes="验证码登录<br/>快速安全登录"></RichText>
        </View>
        <Image
          className="absolute bottom-[11px] right-[18px] w-[167px]"
          src={require("@/assets/image/login/main.png")}
          mode="widthFix"
        />
      </View>

      {/* 表单区域 */}
      <View className="px-5 mt-2">
        <View className="bg-white/95 rounded-2xl p-6 shadow-xl backdrop-blur-sm border border-white/20">
          <View className="mb-5">
            <View className="text-[#1f1f1f] font-bold mb-3 text-base flex items-center">
              <View className="w-1 h-4 bg-[#FDD244] rounded-full mr-2"></View>
              手机号
            </View>
            <View className="bg-[#f8f9fa] rounded-xl p-4 border border-[#e9ecef] flex items-center shadow-sm">
              <Input
                className="bg-transparent text-base text-[#333] flex-1"
                type='number'
                value={recoveryForm.username}
                placeholder='请输入手机号'
                placeholderClass="text-[#999]"
                cursorSpacing={20}
                onInput={e => updateForm({ username: e.detail.value })}
              />
              {recoveryForm.username && (
                <View
                  className="w-6 h-6 bg-[#d1d5db] rounded-full flex items-center justify-center ml-2 active:bg-[#9ca3af]"
                  onClick={() => updateForm({ username: '' })}
                >
                  <View className="text-white text-sm font-bold leading-none">×</View>
                </View>
              )}
            </View>
          </View>
          <View className="mb-2">
            <View className="text-[#1f1f1f] font-bold mb-3 text-base flex items-center">
              <View className="w-1 h-4 bg-[#FDD244] rounded-full mr-2"></View>
              验证码
            </View>
            <View className="bg-[#f8f9fa] rounded-xl p-4 border border-[#e9ecef] flex items-center shadow-sm">
              <Input
                className="bg-transparent text-base text-[#333] flex-1"
                type='number'
                value={recoveryForm.verifyCode}
                placeholder='请输入验证码'
                placeholderClass="text-[#999]"
                cursorSpacing={20}
                onInput={e => updateForm({ verifyCode: e.detail.value })}
              />
              {recoveryForm.verifyCode && (
                <View
                  className="w-6 h-6 bg-[#d1d5db] rounded-full flex items-center justify-center ml-2 mr-2 active:bg-[#9ca3af]"
                  onClick={() => updateForm({ verifyCode: '' })}
                >
                  <View className="text-white text-sm font-bold leading-none">×</View>
                </View>
              )}
              {showSend ? (
                <Button
                  className="px-4 py-2 rounded-full bg-[#FDD244] text-white text-sm leading-none border-0 after:border-none shadow-sm active:bg-[#fdd244e0]"
                  hoverClass="bg-[#fdd244e0]"
                  onClick={sendVerifyCode}
                >
                  获取验证码
                </Button>
              ) : (
                <Label className="px-4 py-2 text-sm text-[#FDD244] leading-none bg-[rgba(253,210,68,0.1)] rounded-full">
                  {countdown}秒后重新发送
                </Label>
              )}
            </View>
          </View>
        </View>
      </View>

      {/* 弹性空间 */}
      <View className="flex-1"></View>

      {/* 登录按钮区域 */}
      <View className="flex flex-col gap-4 items-center text-[#999999] mt-6 px-5 mb-8">
        {!recoveryForm.verifyCode && (
          <View className="text-sm text-[#999] text-center">
            请先获取验证码后再登录
          </View>
        )}
        <Button
          className={`w-full h-12 rounded-full border-0 text-lg font-bold after:border-none text-center flex items-center justify-center shadow-lg ${recoveryForm.verifyCode
            ? 'bg-[#FDD244] text-white active:bg-[#fdd244e0]'
            : 'bg-[#E0E0E0] text-white'
            }`}
          hoverClass={recoveryForm.verifyCode ? "bg-[#fdd244e0] shadow-xl" : ""}
          onClick={loginWithVerityCode}
        >
          登录
        </Button>
        <View className="text-xs text-[#999] text-center px-4">
          登录即表示您已阅读并同意相关用户协议和隐私政策
        </View>
      </View>
    </View>
  );
};

export default PasswordRecoveryPage;
