import { View, Text } from "@tarojs/components";
import Taro, { navigateBack } from "@tarojs/taro";
import styles from "./nav-bar.module.scss";
import { useEffect, useState } from "react";
import { isWeapp } from "@/constant/env";

const Index = ({
  text,
  onBack,
  navBarHeight = 42,
  onHeightChange,
}: {
  text?: string;
  onBack?: () => void;
  navBarHeight?: number;
  onHeightChange?: (height: number) => void;
}) => {
  const handleBack = () => {
    onBack?.() ?? navigateBack();
  };

  const [navHeight, setNavHeight] = useState(0);
  const [statusBarHeight, setStatusBarHeight] = useState(0);

  useEffect(() => {
    // 获取系统信息，只调用一次
    const systemInfo = Taro.getSystemInfoSync();
    const statusHeight = systemInfo.statusBarHeight || 0;
    setStatusBarHeight(statusHeight);

    // 计算导航栏高度
    let contentHeight = navBarHeight; // 默认高度

    // 在小程序环境下，使用胶囊按钮信息计算高度
    if (isWeapp) {
      try {
        // 获取胶囊按钮位置信息
        const menuButtonInfo = Taro.getMenuButtonBoundingClientRect();

        // 计算内容区高度 = 胶囊高度 + 上下边距
        if (menuButtonInfo && menuButtonInfo.height > 0) {
          contentHeight =
            menuButtonInfo.height +
            (menuButtonInfo.top - statusHeight) * 2;
        }
      } catch (error) {
        console.error("获取胶囊按钮信息失败", error);
      }
    }

    // 计算总高度 = 状态栏高度 + 内容区高度
    const totalHeight = statusHeight + contentHeight;
    setNavHeight(contentHeight);

    // 将总高度传递给父组件
    onHeightChange?.(totalHeight);
  }, [navBarHeight, onHeightChange]);

  return (
    <View
      className={styles.customNavBar}
      style={`height: ${navHeight}px;margin-top: ${statusBarHeight}px;`}
    >
      <View className={styles.backButton} onClick={handleBack}>
        <View className="at-icon at-icon-chevron-left"></View>
      </View>
      <Text className={styles.title}>{text}</Text>
    </View>
  );
};
export default Index;
