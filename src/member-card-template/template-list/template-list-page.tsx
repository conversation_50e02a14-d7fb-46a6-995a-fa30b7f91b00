import PageWithNav from '@/view/component/page-with-nav/page-with-nav.component';
import { getMemCardtemplateList } from '@/service/mem/tMemberCardTemplateController/getMemCardtemplateList';
import styles from './template-list-page.module.scss';
import { View, Text, ScrollView } from '@tarojs/components';
import Taro, { useDidShow } from '@tarojs/taro';
import { useEffect, useRef, useState } from 'react';


interface CardType {
  cardType: string;
  selected: boolean;
  color?: string;
}

interface CardGroup {
  cardType: string;
  list: any[];
}

const colorEnmu = [
  { color: '#FFDC7F' },
  { color: '#FFBC2A' },
  { color: '#FFDC7F' },
  { color: '#FFBC2A' },
];

// // 卡类型列表
// const cardTypeList = [
//   { key: 'value', label: '储值卡' },
//   { key: 'times', label: '次卡' },
//   { key: 'discount', label: '折扣卡' },
//   { key: 'package', label: '套餐卡' },
//   { key: 'point', label: '积分卡' },
//   { key: 'timing', label: '计时卡' }
// ];

// // 示例数据结构，实际请替换为你的数据
// const cardData = {
//   value: [
//     { id: 1, name: '会员储值卡', price: 1000, gift: '染发1次' },
//     { id: 2, name: '充值1000元送200元储值卡', price: 1000, giftAmount: 200 },
//     { id: 3, name: '500元储值卡', price: 500, gift: '洗护项目1次' },
//     { id: 4, name: '2000元储值卡', price: 2000, gift: '护理2次' },
//     { id: 5, name: '3000元储值卡', price: 3000, gift: '染发2次' },
//     { id: 6, name: '5000元储值卡', price: 5000, gift: '护理5次' },
//     { id: 7, name: '10000元储值卡', price: 10000, gift: '护理10次' },
//     { id: 8, name: '超值储值卡', price: 8888, gift: '染发3次' }
//   ],
//   times: [
//     { id: 11, name: '10次美甲卡（基础款）', price: 1000, remain: '10次' },
//     { id: 12, name: '20次美甲卡', price: 1800, remain: '20次' },
//     { id: 13, name: '30次美甲卡', price: 2500, remain: '30次' },
//     { id: 14, name: '5次洗护卡', price: 500, remain: '5次' },
//     { id: 15, name: '8次按摩卡', price: 800, remain: '8次' },
//     { id: 16, name: '12次足疗卡', price: 1200, remain: '12次' },
//     { id: 17, name: '15次SPA卡', price: 2000, remain: '15次' },
//     { id: 18, name: '20次剪发卡', price: 1500, remain: '20次' }
//   ],
//   discount: [
//     { id: 21, name: '闺蜜同行卡（85折）', price: 1000, discount: '8.5折' },
//     { id: 22, name: '学生专享卡（9折）', price: 800, discount: '9折' },
//     { id: 23, name: '新客体验卡（95折）', price: 500, discount: '9.5折' },
//     { id: 24, name: 'VIP尊享卡（8折）', price: 2000, discount: '8折' },
//     { id: 25, name: '节日特惠卡（7折）', price: 1500, discount: '7折' },
//     { id: 26, name: '老客户回馈卡（85折）', price: 1200, discount: '8.5折' },
//     { id: 27, name: '周年庆折扣卡（8折）', price: 1800, discount: '8折' }
//   ],
//   package: [
//     { id: 31, name: '染发周期护（染发2次+护理2次）', price: 3980, gift: '护理2次' },
//     { id: 32, name: '美白套餐', price: 2000, gift: '美白2次' },
//     { id: 33, name: '焕肤套餐', price: 2500, gift: '焕肤2次' },
//     { id: 34, name: '美体套餐', price: 3000, gift: '美体2次' },
//     { id: 35, name: '养发套餐', price: 1800, gift: '养发2次' },
//     { id: 36, name: '护肤套餐', price: 2200, gift: '护肤2次' },
//     { id: 37, name: '美甲美睫套餐', price: 1600, gift: '美甲美睫2次' },
//     { id: 38, name: 'SPA套餐', price: 2800, gift: 'SPA2次' }
//   ],
//   point: [
//     { id: 41, name: '积分卡A', price: 100, gift: '积分100' },
//     { id: 42, name: '积分卡B', price: 200, gift: '积分200' },
//     { id: 43, name: '积分卡C', price: 300, gift: '积分300' },
//     { id: 44, name: '积分卡D', price: 400, gift: '积分400' },
//     { id: 45, name: '积分卡E', price: 500, gift: '积分500' },
//     { id: 46, name: '积分卡F', price: 600, gift: '积分600' },
//     { id: 47, name: '积分卡G', price: 700, gift: '积分700' },
//     { id: 48, name: '积分卡H', price: 800, gift: '积分800' }
//   ],
//   timing: [
//     { id: 51, name: '398元10次洗护卡', price: 398, remain: '9次' },
//     { id: 52, name: '600元20次洗护卡', price: 600, remain: '18次' },
//     { id: 53, name: '1200元50次洗护卡', price: 1200, remain: '45次' },
//     { id: 54, name: '2000元100次洗护卡', price: 2000, remain: '98次' },
//     { id: 55, name: '300元5次洗护卡', price: 300, remain: '4次' },
//     { id: 56, name: '800元15次洗护卡', price: 800, remain: '13次' },
//     { id: 57, name: '1500元30次洗护卡', price: 1500, remain: '28次' },
//     { id: 58, name: '500元8次洗护卡', price: 500, remain: '7次' }
//   ]
// };

const TemplateListPage = () => {
  const [cardTypeList, setCardTypeList] = useState<CardType[]>([]);
  const [cardData, setCardData] = useState<CardGroup[]>([]);
  const [scrollIntoViewId, setScrollIntoViewId] = useState<string>('');
  const scrollViewRef = useRef<any>(null);
  const sectionTopsRef = useRef<number[]>([]);

  // 获取数据
  const fetchMemberCardList = () => {
    getMemCardtemplateList({
      tMemberCardTemplate: {},
    }).then((res) => {
      if (!res || !Array.isArray(res)) return;
      const groupedData = res.reduce((acc, item) => {
        const type = item.cardType || '未分类';
        if (!acc[type]) acc[type] = [];
        acc[type].push(item);
        return acc;
      }, {} as Record<string, typeof res>);
      const typeList = Object.keys(groupedData).map((type, index) => ({
        cardType: type,
        selected: index === 0,
        ...colorEnmu[index % 4],
      }));
      const data = Object.entries(groupedData).map(([type, list]) => ({
        cardType: type,
        list: list.map((item) => ({
          ...item,
          id: item.templateId,
          name: item.cardName,
          giftType: item.giftAmount ? 2 : 1,
          giftName: item.setmealList?.[0]?.item || '',
          giftAmount: item?.giftAmount,
          price: item?.retailPrice,
        })),
      }));
      setCardTypeList(typeList);
      setCardData(data);
      setTimeout(() => {
        calcSectionTops();
      }, 300);
    });
  };

  // 计算每个分组的 offsetTop
  const calcSectionTops = () => {
    Taro.createSelectorQuery()
      .selectAll('.card-section')
      .boundingClientRect((rects) => {
        if (Array.isArray(rects)) {
          sectionTopsRef.current = rects.map((r) => r.top);
        }
      })
      .exec();
  };

  useDidShow(() => {
    fetchMemberCardList();
  });

  // 左侧菜单点击
  const handleMenuClick = (index: number) => {
    setCardTypeList(
      cardTypeList.map((item, i) => ({
        ...item,
        selected: i === index,
      }))
    );
    setScrollIntoViewId(`anchor-${index}`);
  };

  // 右侧滚动时自动高亮左侧
  const handleScroll = (e) => {
    const scrollTop = e.detail.scrollTop;
    const tops = sectionTopsRef.current;
    if (!tops.length) return;
    let currentIndex = 0;
    for (let i = 0; i < tops.length; i++) {
      if (scrollTop >= tops[i] - tops[0] - 10) {
        currentIndex = i;
      }
    }
    if (!cardTypeList[currentIndex]?.selected) {
      setCardTypeList(
        cardTypeList.map((item, idx) => ({
          ...item,
          selected: idx === currentIndex,
        }))
      );
    }
  };

  const handleEdit = (card: any, event: any) => {
    event.stopPropagation();
    Taro.redirectTo({
      url: `/member-card-template/create-member-card/create-member-card-page?id=${card.id}&mode=edit`,
    });
  };

  // 处理添加新卡片
  const handleAddCard = () => {
    Taro.navigateTo({
      url: '/member-card-template/create-member-card/create-member-card-page',
    });
  };
  const handleAction = (card: any, event: any) => {
    event.stopPropagation();

    // 这里可以添加导航到下一个页面的逻辑
    console.log('card', card);
    setTimeout(() => {
      Taro.eventCenter.trigger('UPDATE_CARD', { card });
    }, 1);
    Taro.navigateTo({
      url: `/member/addmember/addmember-page?templateId=${card?.id}`,
    });
  };

  return (
    <PageWithNav title="会员卡模板管理" containerClassName={styles.container}>
      {/* 顶部会员卡模板卡片 */}
      <View className={styles.topCardBox}>
        <View className={styles.topCardContent}>
          <View>
            <Text className={styles.topCardTitle}>会员卡模板</Text>
            <Text className={styles.topCardDesc}>多种模板任你选择</Text>
          </View>
          <View className={styles.createBtn} onClick={handleAddCard}>
            <Text className={styles.plusIcon}>＋</Text>
            <Text className={styles.createText}>创建</Text>
          </View>
        </View>
      </View>
      <View className={styles.memberCardPage}>
        {/* 左侧菜单 */}
        <View className={styles.sideMenu}>
          {cardTypeList.map((item, index) => (
            <View
              key={item.cardType}
              className={`${styles.menuItem} ${
                item.selected ? styles.active : ''
              }`}
              onClick={() => handleMenuClick(index)}
            >
              {item.cardType}
            </View>
          ))}
        </View>
        {/* 右侧内容区 */}
        <View className={styles.cardContent}>
          <ScrollView
            scrollY
            ref={scrollViewRef}
            scrollIntoView={scrollIntoViewId}
            onScroll={handleScroll}
            scrollWithAnimation
            style={{ height: '100vh' }}
          >
            {cardData.map((group, idx) => (
              <View
                key={group.cardType}
                id={`anchor-${idx}`}
                className={`card-section ${styles.cardSection}`}
              >
                <View className={styles.sectionTitle}>{group.cardType}</View>
                <View className={styles.cardList}>
                  {group.list.map((card) => (
                    <View key={card.id} className={styles.cardItem} onClick={() => Taro.navigateTo({ url: `/member-card-template/member-card-template-detail/member-card-template-detail-page?id=${card.id}` })}>
                      <View className={styles.cardTitle}>{card.name}</View>
                      <View className={styles.cardInfoRow}>
                        <Text className={styles.cardPrice}>¥{card.price}</Text>
                        <Text className={styles.cardGiftRight}>
                          {card.giftType === 1
                            ? `赠送：${card.giftName}`
                            : `赠送：${card.giftAmount}元`}
                        </Text>
                      </View>
                      <View className={styles.cardBtnRow}>
                        <View
                          className={styles.editBtn}
                          onClick={(event) => handleEdit(card, event)}
                        >
                          编辑
                        </View>
                        <View
                          className={styles.actionBtn}
                          onClick={(event) => handleAction(card, event)}
                        >
                          办理会员
                        </View>
                      </View>
                    </View>
                  ))}
                </View>
              </View>
            ))}
            {/* 底部补白，防止最后一项被遮挡 */}
            <View style={{ height: '200px' }} />
          </ScrollView>
        </View>
      </View>
    </PageWithNav>
  );
};

export default TemplateListPage;
