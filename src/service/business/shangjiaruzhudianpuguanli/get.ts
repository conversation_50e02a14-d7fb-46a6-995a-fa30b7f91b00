/* eslint-disable */
import request from '@/helper/request';

/** 获取用户绑定的商家信息 获取用户绑定的商家信息 GET /business/shop/get-user-bind-business */
export async function get(options?: { [key: string]: any }) {
  return request<BUSINESS.RListUserBindBusinessVo>('/business/shop/get-user-bind-business', {
    method: 'GET',
    headers: {
      traceId: get.traceId,
    },
    ...(options || {}),
  });
}
get.traceId = '90f8e65bcef03118386dc511804a1ce3';
