import { View, Image, Picker, ScrollView, Label, Input } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { AtButton, AtCheckbox, AtIcon } from "taro-ui";
import styles from "./register-business-page.module.scss";
import { useEffect, useMemo, useState } from "react";
import BasicPage from "@/view/component/basic-page/basic-page.components";
import dayjs from "dayjs";
import { postCommonMsgSend } from "@/service/business/gonggongguanli";

interface IFormData {
  businessName: string,
  contactUser: string,
  contactPhone: string,
  region?: string,
  address?: string,
  businessCategory?: string,
  businessStartHours?: string,
  businessEndHours?: string,
  password: string,
  logo?: string
}

const interval = 5;

const DATETIME_RANGE = Array.from({ length: 24 }).map((_, i) => {
  return Array.from({ length: Math.round(60 / interval) }).map((_, j) => {
    return `${i.toString().padStart(2, '0')}:${(j * interval).toString().padStart(2, '0')}`
  })
}).flat()

const Index: React.FC = () => {
  const [formData, setFormData] = useState<IFormData>({
    businessName: '',
    contactUser: '',
    contactPhone: '',
    region: '',
    address: '',
    businessCategory: '',
    businessStartHours: '00:00',
    businessEndHours: '23:55',
    password: '',
  });

  const [categories, setCategories] = useState<any[]>([]);

  const [businessTimeRange, setBusinessTimeRange] = useState<[number, number]>([0, DATETIME_RANGE.length - 1])
  const timeRange = useMemo(() => {
    return `${DATETIME_RANGE[businessTimeRange[0]]} - ${DATETIME_RANGE[businessTimeRange[1]]}`
  }, [businessTimeRange])

  const [isAgreed, setIsAgreed] = useState(false);

  const [tempLogoPath, setTempLogoPath] = useState('');

  const handleChange = (name: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [name]: String(value)
    }));
  };

  const handlePropChange = (prop: Record<string, string | number>) => {
    setFormData(prev => ({
      ...prev,
      ...prop
    }));
  };

  const onCategoryChange = (e) => {
    const index = e.detail.value;
    const category = categories[index];
    handleChange("businessCategory", category.categoryId);
  }

  const getCategoryName = useMemo(() => {
    if (formData.businessCategory && categories.length) {
      const exist = categories.find(item => item.categoryId == formData.businessCategory)
      console.log(exist)
      return exist?.categoryName ?? "";
    } else {
      return ""
    }
  }, [categories, formData.businessCategory])

  const onTimeRangeChange = (e) => {
    const range = e.detail.value
    handlePropChange({
      businessStartHours: DATETIME_RANGE[range[0]],
      businessEndHours: DATETIME_RANGE[range[1]]
    })
    setBusinessTimeRange(range);
  }

  const handleUploadLogo = () => {
    Taro.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: function (res) {
        const tempFilePath = res.tempFilePaths[0];
        setTempLogoPath(tempFilePath);
      },
      fail: function () {
        Taro.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  };

  const sendMessage = async (item: BUSINESS.ReservationRecordListVo) => {
    const result = await postCommonMsgSend({
      "msgType": 1, // 消息类型
      "businessName": item.businessName, // 商家名称
      "cardNumber": item.cardNumber // 会员卡号
    })
    console.log(result, 'result')
  }
  const handleSubmit = async () => {
    if (!isAgreed) {
      Taro.showToast({
        title: '请阅读并同意商家入驻相关条款',
        icon: 'none'
      });
      return;
    }

    // 添加必填校验
    if (!formData.businessName.trim()) {
      Taro.showToast({
        title: '商家名称不能为空',
        icon: 'none'
      });
      return;
    }

    if (!formData.contactUser.trim()) {
      Taro.showToast({
        title: '姓名不能为空',
        icon: 'none'
      });
      return;
    }

    // 电话号码验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!formData.contactPhone.trim()) {
      Taro.showToast({
        title: '手机号码不能为空',
        icon: 'none'
      });
      return;
    } else if (!phoneRegex.test(formData.contactPhone)) {
      Taro.showToast({
        title: '请输入正确的手机号码',
        icon: 'none'
      });
      return;
    }

    if (
      formData.businessStartHours && formData.businessEndHours && (
        dayjs(`2000-01-01 ${formData.businessStartHours}`).isAfter(`2000-01-01 ${formData.businessEndHours}`)
      )
    ) {
      Taro.showToast({
        title: '营业时间错误',
        icon: 'none'
      });
      return;
    }

    if (!formData.password.trim()) {
      Taro.showToast({
        title: '登录密码不能为空',
        icon: 'none'
      });
      return;
    } else if (formData.password.length < 6) {
      Taro.showToast({
        title: '密码长度不能少于6位',
        icon: 'none'
      });
      return;
    }

    if (!tempLogoPath) {
      Taro.showToast({
        title: '请上传店铺LOGO',
        icon: 'none'
      });
      return;
    }

    try {
      Taro.showLoading({
        title: '正在提交',
        mask: true
      });

      // 将图片URL添加到表单数据中
      const submitData = {
        ...formData,
        logo: tempLogoPath
      };

      // 提交表单数据
      const res: any = await service.business.businessController.postShopRegister(submitData);
      Taro.setStorageSync("loginInfo", {
        token: res?.token,
        businessId: res?.businessId,
      });

      Taro.requestSubscribeMessage({
        tmplIds: ['CVPQxV69Assu-Zj5Wojv59rMfhMZnhY_JbTFFxg2tk0'], // 会员加入提醒：会员名称、手机号、注册门店、注册时间、会员卡号
        success(res) {
          console.log('用户订阅成功', res);
          service.business.businessController.getSendRegisterOkMsg().then(res => {
            console.log(res);
          })
        },
        fail(err) {
          console.error('用户订阅失败', err);
        }
      });

      Taro.hideLoading();
      Taro.redirectTo({
        url: "/view/page/tab/home/<USER>",
      });
      console.log(res, 'res')
      console.log(submitData, 'submitData')
      await sendMessage({ ...submitData, ...res })
    } catch (error) {
      Taro.hideLoading();
      Taro.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
      console.error('提交失败:', error);
    }
  };

  const handleAgreementClick = () => {
    Taro.navigateTo({
      url: '/shop/enterprise-certification/enterprise-certification-page'
    });
  };

  const { run: getCategories } = useRequest(() => service.business.shangjiaxiangmuguanli.getShopProjectAllCategory(), {
    manual: true,
    onSuccess: (res) => {
      if (res) {
        setCategories(res)
      }
    }
  })

  useEffect(() => {
    getCategories();
  }, [])

  return (
    <BasicPage
      title="商家入驻"
      showBack={false}
      bottomSafe
    >
      <View className={styles.wrapper}>
        <ScrollView className={styles.form} scrollY>
          <View className={styles.formWrapper}>
            <View className={`at-row ${styles.businessRegisterDiv}`}>
              <Image
                className={styles.businessRegisterImg}
                src={require("@/assets/image/register-business.png")}
              />
            </View>
            <View className={`${styles.formDiv}`}>
              <View className={`${styles.shopInfo}`}>店铺信息</View>
              <View className={styles.formContainer}>
                <View className={styles.formItem}>
                  <Label className={`${styles.label} ${styles.required}`}>店铺名称</Label>
                  <View className={styles.content}>
                    <Input className={styles.control} value={formData.businessName} placeholder="请输入店铺名称" placeholderClass={styles.placeholder} onInput={e => handleChange('businessName', e.detail.value)} />
                  </View>
                </View>
                <View className={styles.formItem}>
                  <Label className={`${styles.label} ${styles.required}`}>姓名</Label>
                  <View className={styles.content}>
                    <Input className={styles.control} value={formData.contactUser} placeholder="请输入姓名" placeholderClass={styles.placeholder} onInput={e => handleChange('contactUser', e.detail.value)} />
                  </View>
                </View>
                <View className={styles.formItem}>
                  <Label className={`${styles.label} ${styles.required}`}>电话</Label>
                  <View className={styles.content}>
                    <Input className={styles.control} type="number" value={formData.contactPhone} placeholder="手机号作为登录账号" placeholderClass={styles.placeholder} onInput={e => handleChange('contactPhone', e.detail.value)} />
                  </View>
                </View>
                <Picker mode='region' onChange={(e) => handleChange('region', e.detail.value.join("/"))}>
                  <View className={styles.formItem}>
                    <Label className={styles.label}>所在地区</Label>
                    <View className={styles.content}>
                      <View className={styles.control}>
                        {
                          formData.region ?
                            <Label>{formData.region}</Label>
                            :
                            <Label className={styles.placeholder}>请选择所在地区</Label>
                        }
                      </View>
                      <AtIcon size='10' value='chevron-right'></AtIcon>
                    </View>
                  </View>
                </Picker>
                <View className={styles.formItem}>
                  <Label className={styles.label}>详细地址</Label>
                  <View className={styles.content}>
                    <Input className={styles.control} value={formData.address} placeholder="请输入详细地址" placeholderClass={styles.placeholder} onInput={e => handleChange('address', e.detail.value)} />
                  </View>
                </View>
                <Picker mode='selector' range={categories} rangeKey="categoryName" onChange={onCategoryChange}>
                  <View className={styles.formItem}>
                    <Label className={styles.label}>行业分类</Label>
                    <View className={styles.content}>
                      <View className={styles.control}>
                        {
                          getCategoryName ?
                            <Label>{getCategoryName}</Label>
                            :
                            <Label className={styles.placeholder}>请选择行业分类</Label>
                        }
                      </View>
                      <AtIcon size='10' value='chevron-right'></AtIcon>
                    </View>
                  </View>
                </Picker>
                <Picker mode='multiSelector' value={businessTimeRange} range={[DATETIME_RANGE, DATETIME_RANGE]} onChange={onTimeRangeChange}>
                  <View className={styles.formItem}>
                    <Label className={styles.label}>营业时间</Label>
                    <View className={styles.content}>
                      <View className={styles.control}>
                        <Label>{timeRange}</Label>
                      </View>
                      <AtIcon size='10' value='chevron-right'></AtIcon>
                    </View>
                  </View>
                </Picker>
                <View className={styles.formItem}>
                  <Label className={`${styles.label} ${styles.required}`}>登录密码</Label>
                  <View className={styles.content}>
                    <Input className={styles.control} value={formData.password} placeholder="请输入至少6位密码" placeholderClass={styles.placeholder} onInput={e => handleChange('password', e.detail.value)} />
                  </View>
                </View>
              </View>
            </View>
            <View className={`${styles.formDiv}`}>
              <View className={`${styles.shopInfo}`}>店铺图片</View>
              <View className={`${styles.shopLogo}`}>店铺LOGO</View>
              <View className={`${styles.shopLogoWarn}`}>1、图片分辨率不超过200*200</View>
              <View className={`${styles.shopLogoWarn}`}>2、大小不超过1M</View>


              <View className={styles.logoContainer}>
                {(tempLogoPath || formData.logo) ? (
                  <Image
                    className={styles.uploadIcon}
                    src={tempLogoPath || formData.logo || ""}
                    mode="aspectFit"
                  />
                ) : (
                  <View onClick={handleUploadLogo} >
                    <Image src={require("@/assets/image/upload-logo.png")} className={styles.uploadIcon}></Image>
                  </View>
                )}
                {(tempLogoPath || formData.logo) && (
                  <AtButton
                    size='small'
                    onClick={handleUploadLogo}
                  >
                    重新上传
                  </AtButton>
                )}
              </View>
            </View>
          </View>
        </ScrollView>
        <View className={styles.submitDiv}>
          <View className={styles.checkboxWrapper}>
            <AtCheckbox className={styles.agreedCheckbox}
              options={[{
                value: 'agreed',
                label: '阅读并同意'
              }]}
              selectedList={isAgreed ? ['agreed'] : []}
              onChange={(selectedList) => setIsAgreed(selectedList.includes('agreed'))}
            />
            <View
              className={styles.agreementLink}
              onClick={handleAgreementClick}
            >
              《商家入驻相关条款》
            </View>
          </View>
          <AtButton
            className={styles.submitBtn}
            type="primary"
            onClick={handleSubmit}
            disabled={!isAgreed}
          >
            注册
          </AtButton>
        </View>
      </View>
    </BasicPage>
  );
};

export default Index;
