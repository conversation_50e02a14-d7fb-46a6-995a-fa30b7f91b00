import Taro from "@tarojs/taro";

export const isWeApp = Taro.getEnv() === Taro.ENV_TYPE.WEAPP;

export const isWeb = Taro.getEnv() === Taro.ENV_TYPE.WEB;

export const isApp = Taro.getEnv() === Taro.ENV_TYPE.RN;

export const isDev = process.env.NODE_ENV === "development";

export const isProd = process.env.NODE_ENV === "production";

// 基于 process.env.TARO_ENV 的平台判断
export const isH5 = process.env.TARO_ENV === "h5";

export const isWeapp = process.env.TARO_ENV === "weapp";

// 服务器路径配置
export const serverPath = process.env.NODE_ENV === "development" ? "" : "";
