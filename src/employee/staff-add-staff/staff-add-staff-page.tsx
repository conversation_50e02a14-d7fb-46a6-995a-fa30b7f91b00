import React, { useEffect, useMemo, useRef, useState } from "react";
import useStore from "@/hook/store";
import Taro, { useDidShow } from "@tarojs/taro";
import { View, Text, Image, ScrollView, Input, Label } from "@tarojs/components";
import { AtButton, AtIcon, AtSwitch } from "taro-ui";
import {
	PageContainerRef,
} from "@/view/component/page-with-nav/page-with-nav.component";
import AvatarImg from "@/assets/image/common/avatar.png";
import CarameImg from "@/assets/image/common/camera.png";
import styles from "./staff-add-staff-page.module.scss";
import BasicPage from "@/view/component/basic-page/basic-page.components";
import dayjs from "dayjs";
import { CacheStore } from "@/store/cache.store";
import { GlobalInfoStore } from "@/store/global-info.store";
import Picker from "@/view/component/common/Picker";
import { EMPLOYEE_POSITION, EMPLOYEE_RANK, getRangeItemL<PERSON>l, SEX_RANGE, SEX_SCHEDULE_STATUS } from "@/constant";
import Switcher from "@/view/component/common/Switcher";
import SettingImg from "@/assets/image/common/setting.png";
import { validateForm } from "@/util/validate-util";

interface IProps {
	businessId?: number,
	avatarUrl?: string | null; // 员工头像
	avatarUrlFile?: File | null; // 员工头像文件
	employeeNickname: string | undefined; // 用户名
	sex: number; // 性别
	employeePhone: string | undefined; // 手机号
	position: string | undefined; // 职位
	status: number; // 0-禁用 1-启用
	onDutyStatus: "ON_DUTY" | "OFF_DUTY"; // ON_DUTY-上班 OFF_DUTY-下班
	rank: "高级" | "中级" | "初级";
	hiredate?: string;
	shiftName: string;
	shiftWholeDay: number;
	shiftStartTime: string;
	shiftEndTime: string;
	weeklyRestType: string;
	weeklyRestDay: string;
}

const Index: React.FC = () => {
	const container: PageContainerRef = useRef(null);
	const cache: CacheStore = useStore().cacheStore;
	const globalCache: GlobalInfoStore = useStore().globalInfoStore;
	const [isEdit, setIsEdit] = useState(false); // 当前是否处于编辑态
	const [employee, setEmployee] = useState<IProps>({
		avatarUrl: null,
		avatarUrlFile: null,
		employeeNickname: undefined,
		sex: 0,
		employeePhone: undefined,
		position: "手艺人",
		status: 1,
		onDutyStatus: "ON_DUTY",
		rank: "中级",
		shiftName: "日班",
		shiftWholeDay: 1,
		shiftStartTime: "00:00",
		shiftEndTime: "23:59",
		weeklyRestType: "自定义",
		weeklyRestDay: ""
	});

	// picker 状态控制
	const [showSexPicker, setShowSexPicker] = useState(false);
	const [showPositionPicker, setShowPositionPicker] = useState(false);
	const [showRankPicker, setShowRankPicker] = useState(false);
	const [showBusinessPicker, setShowBusinessPicker] = useState(false);


	const [businessRange, setBusinessRange] = useState<Array<{ label: string, value: number }>>([])

	// 获取路由参数
	const { id } = Taro.getCurrentInstance().router?.params ?? {};

	const title = useMemo(() => {
		return id ? "编辑员工" : "添加员工"
	}, [id])

	/** 员工编辑信息 */
	useEffect(() => {
		if (id) {
			setIsEdit(true);
			service.business.businessEmployeeController
				.getEmployeeEmployeeId({ employeeId: parseInt(id) })
				.then((data) => {
					data && setEmployee(data);
				});
		}
	}, [id]);

	useDidShow(() => {
		const staffInfo = cache.pageContext;
		if (!staffInfo) {
			return
		}

		setEmployee({ ...staffInfo } as IProps)
	})

	useEffect(() => {
		cache.setPageContext(employee)
	}, [employee])

	useEffect(() => {
		updateState({ businessId: globalCache.businessId });
		setBusinessRange([{ label: globalCache.businessName, value: globalCache.businessId }]);
	}, [globalCache])


	const updateState = (state) => {
		setEmployee({ ...employee, ...state });
	};

	const onLinkTo = () => {
		Taro.navigateTo({
			url: "/employee/staff-management/staff-management-page",
		});
	};

	const toStaffShift = () => {
		Taro.navigateTo({ url: "/employee/staff-shift/staff-shift-page" })
	}

	const onSave = async () => {
		if (check()) {
			Taro.showLoading({
				title: "保存中"
			})
			const API = isEdit ? "putEmployee" : "postEmployee";
			const { avatarUrlFile, avatarUrl, ...rest } = employee;

			// 服务器出错时，会导致loading提醒无法自动关闭，添加try-catch
			try {
				const data = await service.business.businessEmployeeController[API](
					{
						// avatar: avatarUrlFile ?? avatarUrl ?? "", // 新增时优先取文件，否则取图片路径
						...rest,
						hiredate: rest.hiredate || dayjs().format("YYYY-MM-DD")
					},
					avatarUrlFile || undefined
				);
				if (data) {
					Taro.navigateBack()
				} else {
					throw new Error("保存员工失败")
				}
			} catch (error) {
				console.log(error)
				Taro.showToast({
					title: "保存失败",
					icon: "error",
				})
			} finally {
				Taro.hideLoading()
			}
		}
	};

	/** 一些必填校验 */
	const check = (): boolean => {
		return validateForm(employee, [
			{
				key: "employeeNickname",
				validateKey: "REQUIRED",
				onError: () => {
					container.current?.openMessage({
						message: "用户名不能为空",
						type: "warning",
					});
				}
			}, {
				key: "employeePhone",
				validateKey: "REQUIRED",
				onError: () => {
					container.current?.openMessage({
						message: "手机号不能为空",
						type: "warning",
					});
				}
			}, {
				key: "employeePhone",
				validateKey: "PHONE_NUMBER",
				onError: () => {
					container.current?.openMessage({
						message: "请输入正确的手机号格式",
						type: "warning",
					});
				}
			}, {
				key: "position",
				validateKey: "REQUIRED",
				onError: () => {
					container.current?.openMessage({
						message: "职位不能为空",
						type: "warning",
					});
				}
			}
		]);
	};

	// 添加上传图片的方法
	const handleUploadAvatar = () => {
		Taro.chooseImage({
			count: 1,
			sizeType: ["compressed"],
			sourceType: ["album", "camera"],
			success: async (res) => {
				const { tempFiles, tempFilePaths } = res;
				console.log(res)
				console.log("选择的图片路径：", {
					avatarUrlFile: tempFiles[0]?.originalFileObj,
					avatar: tempFilePaths[0],
				});
				updateState({
					avatarUrlFile: tempFiles[0]?.originalFileObj || tempFilePaths[0],
				});
			},
		});
	};

	return (
		<BasicPage
			ref={container}
			title={title}
			bottomSafe
		>
			<View className={styles.wrapper}>
				<ScrollView className={styles.scrollView} scrollY>
					<View className={styles.cards}>
						<View className={styles.card}>
							<View className={styles.avatarSection}>
								<View className={styles.avatarWrapper}>
									<View
										className={styles.avatarImg}
										onClick={handleUploadAvatar}
									>
										<Image
											className={styles.avatar}
											src={employee.avatarUrlFile || employee.avatarUrl || AvatarImg}
											mode="aspectFill"
										/>
										<Image
											className={styles.camera}
											src={CarameImg}
											mode="aspectFill"
										/>
									</View>

									<View className={styles.uploadHint}>
										<Text className={styles.uploadText}>
											上传真实头像更利于审核通过
										</Text>
									</View>
								</View>
							</View>
							<View className={styles.formItem}>
								<View className={`${styles.label} ${styles.required}`}>用户名</View>
								<View className={styles.content}>
									<Input className={styles.input} value={employee.employeeNickname} onInput={e => updateState({ employeeNickname: e.detail.value })} placeholder="请输入姓名" />
								</View>
							</View>
							<View className={styles.formItem}>
								<View className={`${styles.label} ${styles.required}`}>手机号</View>
								<View className={styles.content}>
									<Input className={styles.input} value={employee.employeePhone} type="number" onInput={e => updateState({ employeePhone: e.detail.value })} placeholder="请输入手机号" />
								</View>
							</View>
							<View className={styles.formItem}>
								<View className={styles.label}>性别</View>
								<View className={styles.content} onClick={() => setShowSexPicker(true)}>
									<Label>{getRangeItemLabel(SEX_RANGE, employee.sex)}</Label>
									<AtIcon className={styles.linkIcon} size='10' value='chevron-right'></AtIcon>
								</View>
							</View>
						</View>

						<View className={styles.card}>
							<View className={styles.formItem}>
								<View className={`${styles.label} ${styles.required}`}>岗位</View>
								<View className={styles.content} onClick={() => setShowPositionPicker(true)}>
									<Label>{getRangeItemLabel(EMPLOYEE_POSITION, employee.position)}</Label>
									<AtIcon className={styles.linkIcon} size='10' value='chevron-right'></AtIcon>
								</View>
							</View>
							<View className={styles.formItem}>
								<View className={styles.label}>级别</View>
								<View className={styles.content} onClick={() => setShowRankPicker(true)}>
									<Label>{getRangeItemLabel(EMPLOYEE_RANK, employee.rank)}</Label>
									<AtIcon className={styles.linkIcon} size='10' value='chevron-right'></AtIcon>
								</View>
							</View>
							<View className={styles.formItem}>
								<View className={styles.label}>状态</View>
								<View className={styles.content}>
									<View></View>
									<AtSwitch className={styles.customSwitch} checked={employee.status === 1} color="#FDD244" onChange={checked => updateState({ status: checked ? 1 : 0 })} />
								</View>
							</View>
							<View className={styles.formItem}>
								<View className={styles.label}>排班状态</View>
								<View className={styles.content}>
									<Switcher size="small" value={employee.onDutyStatus} options={SEX_SCHEDULE_STATUS} onChange={val => updateState({ onDutyStatus: val })}></Switcher>
									<Image
										src={SettingImg}
										className={styles.extraIcon}
										mode="aspectFill"
										onClick={toStaffShift}
									/>
								</View>
							</View>
							<View className={styles.formItem}>
								<View className={styles.label}>所属门店</View>
								<View className={styles.content} onClick={() => setShowBusinessPicker(true)}>
									<Label>{getRangeItemLabel(businessRange, employee.businessId)}</Label>
									<AtIcon className={styles.linkIcon} size='10' value='chevron-right'></AtIcon>
								</View>
							</View>
						</View>
					</View>
				</ScrollView>
				<View className={styles.submitBox}>
					<AtButton className={styles.submitBtn} type="primary" onClick={() => onSave()} circle>
						保存
					</AtButton>
				</View>
			</View>
			<Picker show={showSexPicker} data={employee.sex} range={SEX_RANGE} onConfirm={val => updateState({ sex: val })} onClose={() => setShowSexPicker(false)} />
			<Picker show={showPositionPicker} data={employee.position} range={EMPLOYEE_POSITION} onConfirm={val => updateState({ position: val })} onClose={() => setShowPositionPicker(false)} />
			<Picker show={showRankPicker} data={employee.rank} range={EMPLOYEE_RANK} onConfirm={val => updateState({ rank: val })} onClose={() => setShowRankPicker(false)} />
			<Picker show={showBusinessPicker} data={employee.businessId} range={businessRange} onConfirm={val => updateState({ businessId: val })} onClose={() => setShowBusinessPicker(false)} />
		</BasicPage>
	);
};

export default Index;
