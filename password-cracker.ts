/**
 * 密码破解推理程序
 * 基于给定的线索推断出正确的3位数密码
 */

interface Clue {
  guess: string;
  correctCount: number;
  correctPositionCount: number;
  description: string;
}

class PasswordCracker {
  private clues: Clue[] = [
    { guess: '680', correctCount: 1, correctPositionCount: 1, description: '一个号码正确，且位置正确' },
    { guess: '615', correctCount: 1, correctPositionCount: 0, description: '一个号码正确，但位置不正确' },
    { guess: '206', correctCount: 2, correctPositionCount: 0, description: '两个号码正确，但位置都不正确' },
    { guess: '738', correctCount: 0, correctPositionCount: 0, description: '没有一个号码正确' },
    { guess: '078', correctCount: 1, correctPositionCount: 0, description: '一个号码正确，但位置不正确' }
  ];

  private log(message: string): void {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${message}`);
  }

  private validatePassword(password: string): boolean {
    this.log(`开始验证密码: ${password}`);
    
    for (let i = 0; i < this.clues.length; i++) {
      const clue = this.clues[i];
      const result = this.checkClue(password, clue);
      
      if (!result.isValid) {
        this.log(`❌ 密码 ${password} 不符合线索${i + 1} (${clue.guess}): ${result.reason}`);
        return false;
      }
      
      this.log(`✅ 密码 ${password} 符合线索${i + 1} (${clue.guess}): ${result.reason}`);
    }
    
    this.log(`🎉 密码 ${password} 通过所有验证！`);
    return true;
  }

  private checkClue(password: string, clue: Clue): { isValid: boolean; reason: string } {
    const passwordDigits = password.split('');
    const guessDigits = clue.guess.split('');
    
    // 计算正确数字的数量（不考虑位置）
    let correctDigits = 0;
    let correctPositions = 0;
    
    const passwordCount = this.countDigits(password);
    const guessCount = this.countDigits(clue.guess);
    
    // 计算共同数字的数量
    for (const digit in passwordCount) {
      if (guessCount[digit]) {
        correctDigits += Math.min(passwordCount[digit], guessCount[digit]);
      }
    }
    
    // 计算位置正确的数字数量
    for (let i = 0; i < 3; i++) {
      if (passwordDigits[i] === guessDigits[i]) {
        correctPositions++;
      }
    }
    
    const isCorrectCount = correctDigits === clue.correctCount;
    const isCorrectPosition = correctPositions === clue.correctPositionCount;
    
    if (!isCorrectCount) {
      return {
        isValid: false,
        reason: `期望${clue.correctCount}个正确数字，实际${correctDigits}个`
      };
    }
    
    if (!isCorrectPosition) {
      return {
        isValid: false,
        reason: `期望${clue.correctPositionCount}个位置正确，实际${correctPositions}个`
      };
    }
    
    return {
      isValid: true,
      reason: `${correctDigits}个正确数字，${correctPositions}个位置正确`
    };
  }

  private countDigits(str: string): { [key: string]: number } {
    const count: { [key: string]: number } = {};
    for (const digit of str) {
      count[digit] = (count[digit] || 0) + 1;
    }
    return count;
  }

  public solve(): string[] {
    this.log('🔍 开始密码破解推理...');
    this.log('📋 线索分析:');
    
    this.clues.forEach((clue, index) => {
      this.log(`   线索${index + 1}: ${clue.guess} - ${clue.description}`);
    });
    
    this.log('\n🧠 推理过程:');
    
    // 第一步：从线索4排除数字
    this.log('步骤1: 从线索4 (738) 可知，7、3、8都不在密码中');
    const excludedDigits = new Set(['7', '3', '8']);
    
    // 第二步：分析线索1和5确定第三位
    this.log('步骤2: 分析线索1 (680) 和线索5 (078)');
    this.log('   - 线索1: 一个数字正确且位置正确');
    this.log('   - 线索5: 一个数字正确但位置不正确');
    this.log('   - 由于8被排除，线索1中可能正确的是6或0');
    this.log('   - 线索5中由于7、8被排除，只能是0正确但位置不对');
    this.log('   - 如果0在线索5中位置不对（第1位），那么0应该在第3位');
    this.log('   - 这与线索1中0在第3位正确相符');
    this.log('   ✅ 确定：密码第3位是0');
    
    // 第三步：分析线索3确定其他数字
    this.log('步骤3: 分析线索3 (206)');
    this.log('   - 两个数字正确但位置都不对');
    this.log('   - 0在第2位但实际在第3位 ✓');
    this.log('   - 还需要一个数字，可能是2或6');
    this.log('   ✅ 确定：密码包含数字2和0');
    
    // 第四步：分析线索2确定第一位
    this.log('步骤4: 分析线索2 (615)');
    this.log('   - 一个数字正确但位置不对');
    this.log('   - 615包含6、1、5');
    this.log('   - 第一位可能是6、1或5中的一个');
    
    const validPasswords: string[] = [];
    const candidates = ['1', '5', '6'];
    
    this.log('\n🔬 验证候选密码:');
    
    for (const firstDigit of candidates) {
      const password = firstDigit + '20';
      this.log(`\n--- 验证密码 ${password} ---`);
      
      if (this.validatePassword(password)) {
        validPasswords.push(password);
      }
    }
    
    this.log('\n📊 最终结果:');
    if (validPasswords.length > 0) {
      this.log(`🎯 找到 ${validPasswords.length} 个有效密码: ${validPasswords.join(', ')}`);
    } else {
      this.log('❌ 未找到有效密码');
    }
    
    return validPasswords;
  }
}

// 执行密码破解
function main() {
  console.log('🔐 密码破解推理程序启动');
  console.log('=' .repeat(50));
  
  const cracker = new PasswordCracker();
  const solutions = cracker.solve();
  
  console.log('=' .repeat(50));
  console.log(`✨ 程序执行完成，找到 ${solutions.length} 个解`);
  
  if (solutions.length > 0) {
    console.log('🏆 正确答案:', solutions);
  }
}

// 运行程序
main();
